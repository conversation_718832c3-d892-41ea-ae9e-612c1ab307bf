# PowerShell script na vytvorenie Canvas PowerApp package
# Autor: Your Name
# Verzia: 1.0
# Datum: Januar 2024

param(
    [Parameter(Mandatory=$false)]
    [string]$OutputPath = "SharePointListManager_CanvasApp.msapp",
    
    [Parameter(Mandatory=$false)]
    [switch]$Force
)

Write-Host "Creating Canvas PowerApp Package..." -ForegroundColor Green

# Kontrola existencie vystupneho suboru
if ((Test-Path $OutputPath) -and (-not $Force)) {
    Write-Host "File '$OutputPath' already exists. Use -Force parameter to overwrite." -ForegroundColor Yellow
    exit 0
} elseif ((Test-Path $OutputPath) -and $Force) {
    Write-Host "Removing existing file '$OutputPath'" -ForegroundColor Yellow
    Remove-Item $OutputPath -Force
}

# Zoznam suborov pre Canvas App package
$canvasFiles = @(
    "CanvasManifest.json",
    "Connections.json", 
    "DataSources/PowerAppDataList.json",
    "Src/App.fx.yaml",
    "Src/BrowseScreen.fx.yaml",
    "Src/DetailsScreen.fx.yaml",
    "Src/EditScreen.fx.yaml",
    "Resources/PublishInfo.json",
    "pkgs/Wadl/Microsoft.PowerApps.CdsBaseDataSourceInfoProvider.xml",
    "Other/References.json",
    "Other/Entropy.json"
)

# Kontrola existencie vsetkych suborov
Write-Host "Checking file existence..." -ForegroundColor Yellow
$missingFiles = @()

foreach ($file in $canvasFiles) {
    if (-not (Test-Path $file)) {
        $missingFiles += $file
        Write-Host "Missing file: $file" -ForegroundColor Red
    } else {
        Write-Host "File exists: $file" -ForegroundColor Green
    }
}

if ($missingFiles.Count -gt 0) {
    Write-Host "`nMissing files:" -ForegroundColor Red
    $missingFiles | ForEach-Object { Write-Host "   - $_" -ForegroundColor Red }
    Write-Host "`nPlease create all required files first." -ForegroundColor Yellow
    exit 1
}

# Vytvorenie Canvas App package (.msapp)
try {
    Write-Host "`nCreating Canvas App package..." -ForegroundColor Yellow
    
    # Pouzitie .NET System.IO.Compression na vytvorenie ZIP s .msapp extension
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    
    # Vytvorenie docasneho adresara
    $tempDir = [System.IO.Path]::GetTempPath() + [System.Guid]::NewGuid().ToString()
    New-Item -ItemType Directory -Path $tempDir | Out-Null
    
    # Kopirovanie suborov do docasneho adresara so zachovanim struktury
    foreach ($file in $canvasFiles) {
        $sourceFile = $file
        $targetFile = Join-Path $tempDir $file
        $targetDir = Split-Path $targetFile -Parent
        
        # Vytvorenie adresara ak neexistuje
        if (-not (Test-Path $targetDir)) {
            New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
        }
        
        # Kopirovanie suboru
        Copy-Item $sourceFile $targetFile -Force
        Write-Host "Added: $file" -ForegroundColor Cyan
    }
    
    # Vytvorenie .msapp suboru (je to ZIP s inou extension)
    [System.IO.Compression.ZipFile]::CreateFromDirectory($tempDir, $OutputPath)
    
    # Vycistenie docasneho adresara
    Remove-Item $tempDir -Recurse -Force
    
    Write-Host "Canvas App package successfully created: $OutputPath" -ForegroundColor Green
    
} catch {
    Write-Host "Error creating Canvas App package: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Informacie o vytvorenom subore
$appInfo = Get-Item $OutputPath
Write-Host "`nFile information:" -ForegroundColor Cyan
Write-Host "   Name: $($appInfo.Name)" -ForegroundColor White
Write-Host "   Size: $([math]::Round($appInfo.Length / 1KB, 2)) KB" -ForegroundColor White
Write-Host "   Created: $($appInfo.CreationTime)" -ForegroundColor White
Write-Host "   Path: $($appInfo.FullName)" -ForegroundColor White

# Instrukcie na import
Write-Host "`nNext steps to import Canvas App:" -ForegroundColor Yellow
Write-Host "1. Go to https://make.powerapps.com/" -ForegroundColor White
Write-Host "2. Select correct Environment" -ForegroundColor White
Write-Host "3. Click on 'Apps' in left menu" -ForegroundColor White
Write-Host "4. Click on 'Import canvas app' (NOT 'Import a solution')" -ForegroundColor White
Write-Host "5. Upload file: $OutputPath" -ForegroundColor White
Write-Host "6. Configure SharePoint and Office 365 Users connections" -ForegroundColor White
Write-Host "7. Click 'Import'" -ForegroundColor White

Write-Host "`nImportant notes:" -ForegroundColor Yellow
Write-Host "• Use 'Import canvas app' NOT 'Import a solution'" -ForegroundColor White
Write-Host "• Create SharePoint list first using PowerShell_Setup.ps1" -ForegroundColor White
Write-Host "• After import, configure correct SharePoint site URL in data sources" -ForegroundColor White
Write-Host "• Check permissions on SharePoint list" -ForegroundColor White
Write-Host "• Test all functionality after import" -ForegroundColor White

Write-Host "`nCanvas PowerApp Package successfully created!" -ForegroundColor Green
