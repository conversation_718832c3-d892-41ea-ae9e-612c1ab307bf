# PowerShell script na vytvorenie PowerApp solution package
# Autor: Your Name
# Verzia: 1.0
# Datum: Januar 2024

param(
    [Parameter(Mandatory=$false)]
    [string]$OutputPath = "SharePointListManager_Solution.zip",

    [Parameter(Mandatory=$false)]
    [switch]$Force
)

Write-Host "Creating PowerApp Solution Package..." -ForegroundColor Green

# Kontrola existencie vystupneho suboru
if ((Test-Path $OutputPath) -and (-not $Force)) {
    Write-Host "File '$OutputPath' already exists. Use -Force parameter to overwrite." -ForegroundColor Yellow
    exit 0
} elseif ((Test-Path $OutputPath) -and $Force) {
    Write-Host "Removing existing file '$OutputPath'" -ForegroundColor Yellow
    Remove-Item $OutputPath -Force
}

# Zoznam suborov na zabalenie
$filesToZip = @(
    "solution.xml",
    "CanvasManifest.json",
    "Connections.json",
    "DataSources/PowerAppDataList.json",
    "Src/App.fx.yaml",
    "Src/BrowseScreen.fx.yaml",
    "Src/DetailsScreen.fx.yaml",
    "Src/EditScreen.fx.yaml",
    "Resources/PublishInfo.json",
    "pkgs/Wadl/Microsoft.PowerApps.CdsBaseDataSourceInfoProvider.xml",
    "Other/References.json",
    "Other/Entropy.json"
)

# Kontrola existencie vsetkych suborov
Write-Host "Checking file existence..." -ForegroundColor Yellow
$missingFiles = @()

foreach ($file in $filesToZip) {
    if (-not (Test-Path $file)) {
        $missingFiles += $file
        Write-Host "Missing file: $file" -ForegroundColor Red
    } else {
        Write-Host "File exists: $file" -ForegroundColor Green
    }
}

if ($missingFiles.Count -gt 0) {
    Write-Host "`nMissing files:" -ForegroundColor Red
    $missingFiles | ForEach-Object { Write-Host "   - $_" -ForegroundColor Red }
    Write-Host "`nPlease create all required files first." -ForegroundColor Yellow
    exit 1
}

# Vytvorenie ZIP suboru
try {
    Write-Host "`nCreating ZIP file..." -ForegroundColor Yellow

    # Pouzitie .NET System.IO.Compression na vytvorenie ZIP
    Add-Type -AssemblyName System.IO.Compression.FileSystem

    # Vytvorenie docasneho adresara
    $tempDir = [System.IO.Path]::GetTempPath() + [System.Guid]::NewGuid().ToString()
    New-Item -ItemType Directory -Path $tempDir | Out-Null

    # Kopirovanie suborov do docasneho adresara so zachovanim struktury
    foreach ($file in $filesToZip) {
        $sourceFile = $file
        $targetFile = Join-Path $tempDir $file
        $targetDir = Split-Path $targetFile -Parent

        # Vytvorenie adresara ak neexistuje
        if (-not (Test-Path $targetDir)) {
            New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
        }

        # Kopirovanie suboru
        Copy-Item $sourceFile $targetFile -Force
        Write-Host "Added: $file" -ForegroundColor Cyan
    }

    # Vytvorenie ZIP suboru
    [System.IO.Compression.ZipFile]::CreateFromDirectory($tempDir, $OutputPath)

    # Vycistenie docasneho adresara
    Remove-Item $tempDir -Recurse -Force

    Write-Host "ZIP file successfully created: $OutputPath" -ForegroundColor Green

} catch {
    Write-Host "Error creating ZIP file: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Informacie o vytvorenom subore
$zipInfo = Get-Item $OutputPath
Write-Host "`nFile information:" -ForegroundColor Cyan
Write-Host "   Name: $($zipInfo.Name)" -ForegroundColor White
Write-Host "   Size: $([math]::Round($zipInfo.Length / 1KB, 2)) KB" -ForegroundColor White
Write-Host "   Created: $($zipInfo.CreationTime)" -ForegroundColor White
Write-Host "   Path: $($zipInfo.FullName)" -ForegroundColor White

# Instrukcie na import
Write-Host "`nNext steps to import into PowerApps:" -ForegroundColor Yellow
Write-Host "1. Go to https://make.powerapps.com/" -ForegroundColor White
Write-Host "2. Select correct Environment" -ForegroundColor White
Write-Host "3. Click on 'Apps' in left menu" -ForegroundColor White
Write-Host "4. Click on 'Import canvas app'" -ForegroundColor White
Write-Host "5. Upload file: $OutputPath" -ForegroundColor White
Write-Host "6. Configure SharePoint and Office 365 Users connections" -ForegroundColor White
Write-Host "7. Click 'Import'" -ForegroundColor White

Write-Host "`nImportant notes:" -ForegroundColor Yellow
Write-Host "• Create SharePoint list first using PowerShell_Setup.ps1" -ForegroundColor White
Write-Host "• After import, configure correct SharePoint site URL in data sources" -ForegroundColor White
Write-Host "• Check permissions on SharePoint list" -ForegroundColor White
Write-Host "• Test all functionality after import" -ForegroundColor White

Write-Host "`nPowerApp Solution Package successfully created!" -ForegroundColor Green
