{"screenName": "EditScreen", "displayName": "Úprava položky", "description": "Obrazovka pre úpravu existujúcej alebo vytvorenie novej položky", "layout": {"type": "Vertical", "padding": 16, "spacing": 12}, "controls": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "Container", "properties": {"Height": 80, "Fill": "RGBA(0, 120, 212, 1)", "BorderRadius": 0}, "children": [{"name": "BackButton", "type": "<PERSON><PERSON>", "properties": {"Text": "← Zrušiť", "X": 20, "Y": 25, "Width": 80, "Height": 30, "Fill": "RGBA(255, 255, 255, 0.2)", "Color": "RGBA(255, 255, 255, 1)", "BorderColor": "RGBA(255, 255, 255, 1)", "OnSelect": "If(IsBlank(varSelectedItem), Navigate(BrowseScreen, ScreenTransition.Fade), Navigate(DetailsScreen, ScreenTransition.Fade))"}}, {"name": "ScreenTitle", "type": "Label", "properties": {"Text": "If(IsBlank(varSelectedItem), \"Nová položka\", \"Úprava položky\")", "X": 120, "Y": 20, "Width": 200, "Height": 40, "Size": 18, "FontWeight": "Bold", "Color": "RGBA(255, 255, 255, 1)", "Align": "Left"}}, {"name": "SaveButton", "type": "<PERSON><PERSON>", "properties": {"Text": "Uložiť", "X": "<PERSON><PERSON><PERSON>th - 180", "Y": 25, "Width": 70, "Height": 30, "Fill": "RGBA(40, 167, 69, 1)", "Color": "RGBA(255, 255, 255, 1)", "OnSelect": "If(IsBlank(varSelectedItem), \n  // Vytvorenie novej polo<PERSON>('PowerApp Data List', Defaults('PowerApp Data List'), {\n    Title: TitleInput.Text,\n    Description: DescriptionInput.Text,\n    Category: CategoryDropdown.Selected.Value,\n    Status: StatusDropdown.Selected.Value,\n    DueDate: If(IsBlank(DueDatePicker.SelectedDate), Blank(), DueDatePicker.SelectedDate),\n    AssignedTo: If(IsBlank(AssignedToCombo.Selected), <PERSON>lank(), AssignedToCombo.Selected),\n    Progress: Value(ProgressSlider.Value),\n    Tags: TagsInput.Text\n  });\n  Notify(\"Položka bola vytvorená\", NotificationType.Success);\n  Navigate(BrowseScreen, ScreenTransition.Fade),\n  // Úprava existujúcej položky\n  Patch('PowerApp Data List', varSelectedItem, {\n    Title: TitleInput.Text,\n    Description: DescriptionInput.Text,\n    Category: CategoryDropdown.Selected.Value,\n    Status: StatusDropdown.Selected.Value,\n    DueDate: If(IsBlank(DueDatePicker.SelectedDate), <PERSON>lank(), DueDatePicker.SelectedDate),\n    AssignedTo: If(IsBlank(AssignedToCombo.Selected), <PERSON>lank(), AssignedToCombo.Selected),\n    Progress: Value(ProgressSlider.Value),\n    Tags: TagsInput.Text\n  });\n  Notify(\"Položka bola aktualizovaná\", NotificationType.Success);\n  Navigate(DetailsScreen, ScreenTransition.Fade)\n)"}}, {"name": "DeleteButton", "type": "<PERSON><PERSON>", "properties": {"Text": "Zmazať", "X": "<PERSON><PERSON><PERSON>th - 100", "Y": 25, "Width": 80, "Height": 30, "Fill": "RGBA(220, 53, 69, 1)", "Color": "RGBA(255, 255, 255, 1)", "Visible": "!IsBlank(varSelectedItem)", "OnSelect": "If(\n  ConfirmDialog.Confirm(\"Naozaj chcete zmazať túto položku?\", \"Zmazanie položky\", \"Zmazať\", \"Zrušiť\"),\n  Remove('PowerApp Data List', varSelectedItem);\n  Notify(\"Položka bola zmazaná\", NotificationType.Success);\n  Navigate(BrowseScreen, ScreenTransition.Fade)\n)"}}]}, {"name": "FormContainer", "type": "Container", "properties": {"Y": 90, "Height": "Parent.Height - 90", "Fill": "RGBA(248, 249, 250, 1)", "Padding": 20}, "children": [{"name": "BasicInfoSection", "type": "Container", "properties": {"Height": 180, "Fill": "RGBA(255, 255, 255, 1)", "BorderColor": "RGBA(222, 226, 230, 1)", "BorderThickness": 1, "BorderRadius": 8, "Padding": 16}, "children": [{"name": "TitleLabel", "type": "Label", "properties": {"Text": "Názov *", "X": 0, "Y": 0, "Width": 100, "Height": 25, "Size": 12, "FontWeight": "SemiBold", "Color": "RGBA(33, 37, 41, 1)"}}, {"name": "TitleInput", "type": "TextInput", "properties": {"Default": "If(IsBlank(varSelectedItem), \"\", varSelectedItem.Title)", "HintText": "Zadajte názov položky", "X": 0, "Y": 30, "Width": "Pa<PERSON><PERSON>Width - 20", "Height": 35, "BorderColor": "RGBA(206, 212, 218, 1)", "Size": 12}}, {"name": "DescriptionLabel", "type": "Label", "properties": {"Text": "<PERSON><PERSON>", "X": 0, "Y": 80, "Width": 100, "Height": 25, "Size": 12, "FontWeight": "SemiBold", "Color": "RGBA(33, 37, 41, 1)"}}, {"name": "DescriptionInput", "type": "TextInput", "properties": {"Default": "If(IsBlank(varSelectedItem), \"\", varSelectedItem.Description)", "HintText": "Zadajte popis <PERSON>", "Mode": "MultiLine", "X": 0, "Y": 110, "Width": "Pa<PERSON><PERSON>Width - 20", "Height": 60, "BorderColor": "RGBA(206, 212, 218, 1)", "Size": 12}}]}, {"name": "StatusSection", "type": "Container", "properties": {"Y": 200, "Height": 120, "Fill": "RGBA(255, 255, 255, 1)", "BorderColor": "RGBA(222, 226, 230, 1)", "BorderThickness": 1, "BorderRadius": 8, "Padding": 16}, "children": [{"name": "CategoryLabel", "type": "Label", "properties": {"Text": "Kategória *", "X": 0, "Y": 0, "Width": 100, "Height": 25, "Size": 12, "FontWeight": "SemiBold", "Color": "RGBA(33, 37, 41, 1)"}}, {"name": "CategoryDropdown", "type": "Dropdown", "properties": {"Items": "[\"Vysoká priorita\", \"Stred<PERSON> priorita\", \"Nízka priorita\"]", "DefaultSelectedItems": "If(IsBlank(varSelectedItem), [\"Stredná priorita\"], [varSelectedItem.Category])", "X": 0, "Y": 30, "Width": 200, "Height": 35, "Size": 12}}, {"name": "StatusLabel", "type": "Label", "properties": {"Text": "Stav *", "X": 220, "Y": 0, "Width": 100, "Height": 25, "Size": 12, "FontWeight": "SemiBold", "Color": "RGBA(33, 37, 41, 1)"}}, {"name": "StatusDropdown", "type": "Dropdown", "properties": {"Items": "[\"<PERSON><PERSON>\", \"V riešení\", \"Dokončený\", \"Zrušený\"]", "DefaultSelectedItems": "If(IsBlank(varSelectedItem), [\"Nový\"], [varSelectedItem.Status])", "X": 220, "Y": 30, "Width": 200, "Height": 35, "Size": 12}}, {"name": "ProgressLabel", "type": "Label", "properties": {"Text": "Pokrok (%)", "X": 0, "Y": 75, "Width": 100, "Height": 25, "Size": 12, "FontWeight": "SemiBold", "Color": "RGBA(33, 37, 41, 1)"}}, {"name": "ProgressSlider", "type": "Slide<PERSON>", "properties": {"Default": "If(IsBlank(varSelectedItem), 0, varSelectedItem.Progress)", "Min": 0, "Max": 100, "Step": 5, "X": 110, "Y": 75, "Width": 200, "Height": 25, "HandleFill": "RGBA(0, 120, 212, 1)", "RailFill": "RGBA(222, 226, 230, 1)"}}, {"name": "ProgressValue", "type": "Label", "properties": {"Text": "ProgressSlider.Value & \"%\"", "X": 320, "Y": 75, "Width": 50, "Height": 25, "Size": 12, "Color": "RGBA(33, 37, 41, 1)", "Align": "Center"}}]}, {"name": "DetailsSection", "type": "Container", "properties": {"Y": 340, "Height": 120, "Fill": "RGBA(255, 255, 255, 1)", "BorderColor": "RGBA(222, 226, 230, 1)", "BorderThickness": 1, "BorderRadius": 8, "Padding": 16}, "children": [{"name": "DueDateLabel", "type": "Label", "properties": {"Text": "<PERSON><PERSON><PERSON>", "X": 0, "Y": 0, "Width": 150, "Height": 25, "Size": 12, "FontWeight": "SemiBold", "Color": "RGBA(33, 37, 41, 1)"}}, {"name": "DueDatePicker", "type": "DatePicker", "properties": {"DefaultDate": "If(IsBlank(varSelectedItem) || IsBlank(varSelectedItem.DueDate), Blank(), varSelectedItem.DueDate)", "X": 0, "Y": 30, "Width": 200, "Height": 35, "Size": 12, "Format": "dd.mm.yyyy"}}, {"name": "AssignedToLabel", "type": "Label", "properties": {"Text": "Priradené komu", "X": 220, "Y": 0, "Width": 150, "Height": 25, "Size": 12, "FontWeight": "SemiBold", "Color": "RGBA(33, 37, 41, 1)"}}, {"name": "AssignedToCombo", "type": "ComboBox", "properties": {"Items": "Office365Users.SearchUser({searchTerm: \"\"}).value", "DefaultSelectedItems": "If(IsBlank(varSelectedItem) || IsBlank(varSelectedItem.AssignedTo), [], [varSelectedItem.AssignedTo])", "DisplayFields": "[\"DisplayName\"]", "SearchFields": "[\"DisplayName\", \"Mail\"]", "X": 220, "Y": 30, "Width": 200, "Height": 35, "Size": 12}}, {"name": "TagsLabel", "type": "Label", "properties": {"Text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "X": 0, "Y": 75, "Width": 100, "Height": 25, "Size": 12, "FontWeight": "SemiBold", "Color": "RGBA(33, 37, 41, 1)"}}, {"name": "TagsInput", "type": "TextInput", "properties": {"Default": "If(IsBlank(varSelectedItem), \"\", varSelectedItem.Tags)", "HintText": "Štít<PERSON> oddele<PERSON>", "X": 110, "Y": 75, "Width": 310, "Height": 35, "BorderColor": "RGBA(206, 212, 218, 1)", "Size": 12}}]}]}], "onVisible": "// Inicializácia formulára pri zobrazení obrazovky", "variables": [{"name": "varSelectedItem", "type": "Record", "description": "Vybraná položka na úpravu (prázdne pre novú položku)"}], "validation": {"rules": [{"field": "TitleInput", "required": true, "message": "Názov je povinný"}, {"field": "CategoryDropdown", "required": true, "message": "Kategória je povinná"}, {"field": "StatusDropdown", "required": true, "message": "<PERSON><PERSON> je povinný"}]}}