{"screenName": "DetailsScreen", "displayName": "Detail <PERSON>ky", "description": "Obrazovka zobrazujúca detailné informácie o vybranej položke", "layout": {"type": "Vertical", "padding": 16, "spacing": 12}, "controls": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "Container", "properties": {"Height": 80, "Fill": "RGBA(0, 120, 212, 1)", "BorderRadius": 0}, "children": [{"name": "BackButton", "type": "<PERSON><PERSON>", "properties": {"Text": "← Späť", "X": 20, "Y": 25, "Width": 80, "Height": 30, "Fill": "RGBA(255, 255, 255, 0.2)", "Color": "RGBA(255, 255, 255, 1)", "BorderColor": "RGBA(255, 255, 255, 1)", "OnSelect": "Navigate(BrowseScreen, ScreenTransition.Fade)"}}, {"name": "ScreenTitle", "type": "Label", "properties": {"Text": "Detail <PERSON>ky", "X": 120, "Y": 20, "Width": 200, "Height": 40, "Size": 18, "FontWeight": "Bold", "Color": "RGBA(255, 255, 255, 1)", "Align": "Left"}}, {"name": "EditButton", "type": "<PERSON><PERSON>", "properties": {"Text": "Upraviť", "X": "<PERSON><PERSON><PERSON>idth - 120", "Y": 25, "Width": 100, "Height": 30, "Fill": "RGBA(255, 193, 7, 1)", "Color": "RGBA(33, 37, 41, 1)", "OnSelect": "Navigate(EditScreen, ScreenTransition.Fade)"}}]}, {"name": "ContentContainer", "type": "Container", "properties": {"Y": 90, "Height": "Parent.Height - 90", "Fill": "RGBA(248, 249, 250, 1)", "Padding": 20}, "children": [{"name": "TitleSection", "type": "Container", "properties": {"Height": 80, "Fill": "RGBA(255, 255, 255, 1)", "BorderColor": "RGBA(222, 226, 230, 1)", "BorderThickness": 1, "BorderRadius": 8, "Padding": 16}, "children": [{"name": "TitleLabel", "type": "Label", "properties": {"Text": "varSelectedItem.Title", "X": 0, "Y": 0, "Width": "<PERSON><PERSON><PERSON>", "Height": 30, "Size": 20, "FontWeight": "Bold", "Color": "RGBA(33, 37, 41, 1)"}}, {"name": "CreatedInfo", "type": "Label", "properties": {"Text": "\"Vytvorené: \" & Text(varSelectedItem.Created, \"dd.mm.yyyy hh:mm\") & \" | Autor: \" & varSelectedItem.Author.DisplayName", "X": 0, "Y": 35, "Width": "<PERSON><PERSON><PERSON>", "Height": 25, "Size": 11, "Color": "RGBA(108, 117, 125, 1)"}}]}, {"name": "StatusSection", "type": "Container", "properties": {"Y": 100, "Height": 120, "Fill": "RGBA(255, 255, 255, 1)", "BorderColor": "RGBA(222, 226, 230, 1)", "BorderThickness": 1, "BorderRadius": 8, "Padding": 16}, "children": [{"name": "StatusContainer", "type": "Container", "properties": {"Height": 40, "Width": "<PERSON><PERSON><PERSON>"}, "children": [{"name": "StatusLabelTitle", "type": "Label", "properties": {"Text": "Stav:", "X": 0, "Y": 0, "Width": 80, "Height": 30, "Size": 12, "FontWeight": "SemiBold", "Color": "RGBA(33, 37, 41, 1)"}}, {"name": "StatusBadge", "type": "Label", "properties": {"Text": "varSelectedItem.Status", "X": 90, "Y": 5, "Width": 120, "Height": 25, "Size": 11, "Color": "RGBA(255, 255, 255, 1)", "Fill": "Switch(varSelectedItem.Status, \"Do<PERSON><PERSON><PERSON><PERSON>\", RGBA(40, 167, 69, 1), \"V riešení\", RGBA(0, 120, 212, 1), \"Nový\", RGBA(255, 193, 7, 1), \"<PERSON><PERSON>šený\", RGBA(220, 53, 69, 1), RGBA(108, 117, 125, 1))", "Align": "Center", "BorderRadius": 12}}, {"name": "CategoryLabelTitle", "type": "Label", "properties": {"Text": "Kategória:", "X": 230, "Y": 0, "Width": 80, "Height": 30, "Size": 12, "FontWeight": "SemiBold", "Color": "RGBA(33, 37, 41, 1)"}}, {"name": "CategoryBadge", "type": "Label", "properties": {"Text": "varSelectedItem.Category", "X": 320, "Y": 5, "Width": 120, "Height": 25, "Size": 11, "Color": "RGBA(255, 255, 255, 1)", "Fill": "Switch(varSelectedItem.Category, \"Vysoká priorita\", RGBA(220, 53, 69, 1), \"Stredná priorita\", RGBA(255, 193, 7, 1), \"Nízka priorita\", RGBA(40, 167, 69, 1), RGBA(108, 117, 125, 1))", "Align": "Center", "BorderRadius": 12}}]}, {"name": "ProgressContainer", "type": "Container", "properties": {"Y": 50, "Height": 60, "Width": "<PERSON><PERSON><PERSON>"}, "children": [{"name": "ProgressLabelTitle", "type": "Label", "properties": {"Text": "Pokrok:", "X": 0, "Y": 0, "Width": 80, "Height": 25, "Size": 12, "FontWeight": "SemiBold", "Color": "RGBA(33, 37, 41, 1)"}}, {"name": "ProgressValue", "type": "Label", "properties": {"Text": "varSelectedItem.Progress & \"%\"", "X": 90, "Y": 0, "Width": 60, "Height": 25, "Size": 12, "Color": "RGBA(33, 37, 41, 1)"}}, {"name": "ProgressBarBackground", "type": "Rectangle", "properties": {"X": 0, "Y": 30, "Width": "Pa<PERSON><PERSON>Width - 20", "Height": 8, "Fill": "RGBA(222, 226, 230, 1)", "BorderThickness": 0, "BorderRadius": 4}}, {"name": "ProgressBarFill", "type": "Rectangle", "properties": {"X": 0, "Y": 30, "Width": "(Parent.Width - 20) * (varSelectedItem.Progress / 100)", "Height": 8, "Fill": "RGBA(0, 120, 212, 1)", "BorderThickness": 0, "BorderRadius": 4}}]}]}, {"name": "DetailsSection", "type": "Container", "properties": {"Y": 240, "Height": 200, "Fill": "RGBA(255, 255, 255, 1)", "BorderColor": "RGBA(222, 226, 230, 1)", "BorderThickness": 1, "BorderRadius": 8, "Padding": 16}, "children": [{"name": "DescriptionTitle", "type": "Label", "properties": {"Text": "Popis:", "X": 0, "Y": 0, "Width": 100, "Height": 25, "Size": 12, "FontWeight": "SemiBold", "Color": "RGBA(33, 37, 41, 1)"}}, {"name": "DescriptionText", "type": "Label", "properties": {"Text": "If(IsBlank(varSelectedItem.Description), \"Bez popisu\", varSelectedItem.Description)", "X": 0, "Y": 30, "Width": "Pa<PERSON><PERSON>Width - 20", "Height": 100, "Size": 11, "Color": "RGBA(33, 37, 41, 1)", "Align": "TopLeft", "AutoHeight": true}}, {"name": "DueDateTitle", "type": "Label", "properties": {"Text": "<PERSON><PERSON><PERSON>:", "X": 0, "Y": 140, "Width": 150, "Height": 25, "Size": 12, "FontWeight": "SemiBold", "Color": "RGBA(33, 37, 41, 1)"}}, {"name": "DueDateValue", "type": "Label", "properties": {"Text": "If(IsBlank(varSelectedItem.DueDate), \"<PERSON>e je stano<PERSON>\", Text(varSelectedItem.DueDate, \"dd.mm.yyyy\"))", "X": 160, "Y": 140, "Width": 200, "Height": 25, "Size": 11, "Color": "If(varSelectedItem.DueDate < Today() && varSelectedItem.Status <> \"Dokončený\", RGBA(220, 53, 69, 1), RGBA(33, 37, 41, 1))"}}, {"name": "AssignedToTitle", "type": "Label", "properties": {"Text": "Priradené komu:", "X": 0, "Y": 170, "Width": 150, "Height": 25, "Size": 12, "FontWeight": "SemiBold", "Color": "RGBA(33, 37, 41, 1)"}}, {"name": "AssignedToValue", "type": "Label", "properties": {"Text": "If(IsBlank(varSelectedItem.AssignedTo), \"<PERSON>e je p<PERSON>rade<PERSON>\", varSelectedItem.AssignedTo.DisplayName)", "X": 160, "Y": 170, "Width": 200, "Height": 25, "Size": 11, "Color": "RGBA(33, 37, 41, 1)"}}]}, {"name": "TagsSection", "type": "Container", "properties": {"Y": 460, "Height": 80, "Fill": "RGBA(255, 255, 255, 1)", "BorderColor": "RGBA(222, 226, 230, 1)", "BorderThickness": 1, "BorderRadius": 8, "Padding": 16}, "children": [{"name": "TagsTitle", "type": "Label", "properties": {"Text": "Štítky:", "X": 0, "Y": 0, "Width": 100, "Height": 25, "Size": 12, "FontWeight": "SemiBold", "Color": "RGBA(33, 37, 41, 1)"}}, {"name": "TagsValue", "type": "Label", "properties": {"Text": "If(IsBlank(varSelectedItem.Tags), \"Žiadne štítky\", varSelectedItem.Tags)", "X": 0, "Y": 30, "Width": "Pa<PERSON><PERSON>Width - 20", "Height": 40, "Size": 11, "Color": "RGBA(33, 37, 41, 1)", "AutoHeight": true}}]}]}], "onVisible": "If(IsBlank(varSelectedItem), Navigate(BrowseScreen, ScreenTransition.Fade))", "variables": [{"name": "varSelectedItem", "type": "Record", "description": "Vybraná položka zo SharePoint listu"}]}