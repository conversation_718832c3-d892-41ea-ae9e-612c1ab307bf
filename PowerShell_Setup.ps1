# PowerShell script na automatické vytvorenie SharePoint listu pre PowerApp
# Autor: Your Name
# Verzia: 1.0
# Dátum: Január 2024

# Parametre
param(
    [Parameter(Mandatory=$true)]
    [string]$SiteUrl,
    
    [Parameter(Mandatory=$false)]
    [string]$ListName = "PowerApp Data List",
    
    [Parameter(Mandatory=$false)]
    [switch]$Force
)

# Import PnP PowerShell modulu
try {
    Import-Module PnP.PowerShell -ErrorAction Stop
    Write-Host "✅ PnP PowerShell modul úspešne načítaný" -ForegroundColor Green
} catch {
    Write-Host "❌ Chyba pri načítaní PnP PowerShell modulu. Inštalujte ho pomocou: Install-Module PnP.PowerShell" -ForegroundColor Red
    exit 1
}

# Pripojenie na SharePoint
try {
    Write-Host "🔗 Pripájam sa na SharePoint site: $SiteUrl" -ForegroundColor Yellow
    Connect-PnPOnline -Url $SiteUrl -Interactive
    Write-Host "✅ Úspešne pripojený na SharePoint" -ForegroundColor Green
} catch {
    Write-Host "❌ Chyba pri pripojení na SharePoint: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Kontrola existencie listu
$existingList = Get-PnPList -Identity $ListName -ErrorAction SilentlyContinue

if ($existingList -and -not $Force) {
    Write-Host "⚠️  List '$ListName' už existuje. Použite parameter -Force pre prepísanie." -ForegroundColor Yellow
    exit 0
} elseif ($existingList -and $Force) {
    Write-Host "🗑️  Mažem existujúci list '$ListName'" -ForegroundColor Yellow
    Remove-PnPList -Identity $ListName -Force
}

# Vytvorenie nového listu
try {
    Write-Host "📝 Vytváram nový list '$ListName'" -ForegroundColor Yellow
    $list = New-PnPList -Title $ListName -Template GenericList -EnableVersioning
    Write-Host "✅ List úspešne vytvorený" -ForegroundColor Green
} catch {
    Write-Host "❌ Chyba pri vytváraní listu: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Pridanie custom stĺpcov
Write-Host "🔧 Pridávam custom stĺpce..." -ForegroundColor Yellow

try {
    # Description - Multiple lines of text
    Add-PnPField -List $ListName -DisplayName "Popis" -InternalName "Description" -Type Note -AddToDefaultView

    # Category - Choice field
    $categoryChoices = @("Vysoká priorita", "Stredná priorita", "Nízka priorita")
    Add-PnPField -List $ListName -DisplayName "Kategória" -InternalName "Category" -Type Choice -Choices $categoryChoices -DefaultValue "Stredná priorita" -AddToDefaultView

    # Status - Choice field  
    $statusChoices = @("Nový", "V riešení", "Dokončený", "Zrušený")
    Add-PnPField -List $ListName -DisplayName "Stav" -InternalName "Status" -Type Choice -Choices $statusChoices -DefaultValue "Nový" -AddToDefaultView

    # DueDate - Date field
    Add-PnPField -List $ListName -DisplayName "Termín dokončenia" -InternalName "DueDate" -Type DateTime -DateTimeFormat DateOnly -AddToDefaultView

    # AssignedTo - Person field
    Add-PnPField -List $ListName -DisplayName "Priradené komu" -InternalName "AssignedTo" -Type User -AddToDefaultView

    # Progress - Number field
    Add-PnPField -List $ListName -DisplayName "Pokrok (%)" -InternalName "Progress" -Type Number -Min 0 -Max 100 -DefaultValue 0 -AddToDefaultView

    # Tags - Single line of text
    Add-PnPField -List $ListName -DisplayName "Štítky" -InternalName "Tags" -Type Text -MaxLength 500

    Write-Host "✅ Všetky stĺpce úspešne pridané" -ForegroundColor Green

} catch {
    Write-Host "❌ Chyba pri pridávaní stĺpcov: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Vytvorenie custom views
Write-Host "👁️  Vytváram custom views..." -ForegroundColor Yellow

try {
    # Active Items view
    $activeItemsFields = @("Title", "Category", "Status", "DueDate", "Progress")
    $activeItemsQuery = "<Where><And><Ne><FieldRef Name='Status'/><Value Type='Choice'>Dokončený</Value></Ne><Ne><FieldRef Name='Status'/><Value Type='Choice'>Zrušený</Value></Ne></And></Where><OrderBy><FieldRef Name='DueDate' Ascending='TRUE'/></OrderBy>"
    Add-PnPView -List $ListName -Title "Aktívne položky" -Fields $activeItemsFields -Query $activeItemsQuery

    # High Priority view
    $highPriorityFields = @("Title", "Status", "DueDate", "AssignedTo", "Progress")
    $highPriorityQuery = "<Where><Eq><FieldRef Name='Category'/><Value Type='Choice'>Vysoká priorita</Value></Eq></Where><OrderBy><FieldRef Name='DueDate' Ascending='TRUE'/></OrderBy>"
    Add-PnPView -List $ListName -Title "Vysoká priorita" -Fields $highPriorityFields -Query $highPriorityQuery

    Write-Host "✅ Custom views úspešne vytvorené" -ForegroundColor Green

} catch {
    Write-Host "❌ Chyba pri vytváraní views: $($_.Exception.Message)" -ForegroundColor Red
}

# Nastavenie permissions (voliteľné)
Write-Host "🔐 Nastavujem permissions..." -ForegroundColor Yellow

try {
    # Povoliť versioning
    Set-PnPList -Identity $ListName -EnableVersioning $true -EnableMinorVersions $false
    
    Write-Host "✅ Permissions úspešne nastavené" -ForegroundColor Green

} catch {
    Write-Host "⚠️  Upozornenie pri nastavovaní permissions: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Pridanie ukážkových dát
Write-Host "📊 Pridávam ukážkové dáta..." -ForegroundColor Yellow

try {
    $sampleData = @(
        @{
            Title = "Implementácia novej funkcionality"
            Description = "Potrebujeme implementovať novú funkcionalitu pre správu používateľov"
            Category = "Vysoká priorita"
            Status = "V riešení"
            Progress = 25
            Tags = "development, users, high-priority"
        },
        @{
            Title = "Aktualizácia dokumentácie"
            Description = "Aktualizovať dokumentáciu pre nové API endpoints"
            Category = "Stredná priorita" 
            Status = "Nový"
            Progress = 0
            Tags = "documentation, api"
        },
        @{
            Title = "Testovanie aplikácie"
            Description = "Kompletné testovanie aplikácie pred produkčným nasadením"
            Category = "Vysoká priorita"
            Status = "Dokončený"
            Progress = 100
            Tags = "testing, production, qa"
        }
    )

    foreach ($item in $sampleData) {
        Add-PnPListItem -List $ListName -Values $item
    }

    Write-Host "✅ Ukážkové dáta úspešne pridané" -ForegroundColor Green

} catch {
    Write-Host "⚠️  Upozornenie pri pridávaní ukážkových dát: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Finálne informácie
Write-Host "`n🎉 SharePoint list úspešne vytvorený!" -ForegroundColor Green
Write-Host "📍 URL listu: $SiteUrl/Lists/$($ListName.Replace(' ', ''))" -ForegroundColor Cyan
Write-Host "📝 Názov listu: $ListName" -ForegroundColor Cyan
Write-Host "`n📋 Ďalšie kroky:" -ForegroundColor Yellow
Write-Host "1. Prejdite na PowerApps (https://make.powerapps.com/)" -ForegroundColor White
Write-Host "2. Vytvorte novú Canvas aplikáciu" -ForegroundColor White
Write-Host "3. Pripojte sa na SharePoint list '$ListName'" -ForegroundColor White
Write-Host "4. Použite poskytnuté JSON súbory ako referenciu pre implementáciu" -ForegroundColor White

# Odpojenie
Disconnect-PnPOnline
Write-Host "`n✅ Script dokončený úspešne!" -ForegroundColor Green
