[{"name": "shared_sharepointonline", "id": "/providers/Microsoft.PowerApps/apis/shared_sharepointonline", "type": "Microsoft.PowerApps/apis", "creationType": "Existing", "dependencyType": "Required", "displayName": "SharePoint", "description": "SharePoint helps organizations share and collaborate with colleagues, partners, and customers. You can connect to SharePoint Online or to an on-premises SharePoint 2013 or 2016 farm using the On-Premises Data Gateway to manage documents and list items.", "iconUri": "https://connectoricons-prod.azureedge.net/releases/v1.0.1549/1.0.1549.2680/sharepointonline/icon.png", "tier": "Standard", "isCustomApi": false, "capabilities": ["actions", "triggers"]}, {"name": "shared_office365users", "id": "/providers/Microsoft.PowerApps/apis/shared_office365users", "type": "Microsoft.PowerApps/apis", "creationType": "Existing", "dependencyType": "Required", "displayName": "Office 365 Users", "description": "Office 365 Users Connection provider lets you access user profiles in your organization using your Office 365 account. You can perform various actions such as get your profile, a user's profile, a user's manager or direct reports.", "iconUri": "https://connectoricons-prod.azureedge.net/releases/v1.0.1549/1.0.1549.2680/office365users/icon.png", "tier": "Standard", "isCustomApi": false, "capabilities": ["actions"]}]