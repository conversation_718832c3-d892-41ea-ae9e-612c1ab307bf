{"dataSources": [{"name": "SharePointConnection", "displayName": "SharePoint List Connection", "type": "SharePoint", "version": "1.0", "description": "Pripojenie na SharePoint list pre PowerApp aplikáciu", "connectionParameters": {"server": "https://{tenant}.sharepoint.com", "site": "/sites/{site-name}", "authType": "Office365", "timeout": 30000, "retryPolicy": {"retryCount": 3, "retryInterval": 1000, "exponentialBackoff": true}}, "entities": [{"name": "PowerApp Data List", "displayName": "PowerApp Data List", "type": "List", "operations": {"create": true, "read": true, "update": true, "delete": true}, "fields": [{"name": "ID", "displayName": "ID", "type": "Number", "primaryKey": true, "required": false, "readOnly": true}, {"name": "Title", "displayName": "N<PERSON>zov", "type": "Text", "required": true, "maxLength": 255, "validation": {"required": true, "minLength": 1, "maxLength": 255}}, {"name": "Description", "displayName": "<PERSON><PERSON>", "type": "Note", "required": false, "richText": true}, {"name": "Category", "displayName": "Kategória", "type": "Choice", "required": true, "choices": ["Vysoká priorita", "Stredná priorita", "Nízka priorita"], "defaultValue": "Stredná priorita"}, {"name": "Status", "displayName": "Stav", "type": "Choice", "required": true, "choices": ["Nový", "V riešení", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "defaultValue": "Nový"}, {"name": "DueDate", "displayName": "<PERSON><PERSON><PERSON>", "type": "DateTime", "required": false, "format": "DateOnly"}, {"name": "AssignedTo", "displayName": "Priradené komu", "type": "User", "required": false, "allowMultipleValues": false}, {"name": "Progress", "displayName": "Pokrok (%)", "type": "Number", "required": false, "min": 0, "max": 100, "defaultValue": 0}, {"name": "Tags", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "Text", "required": false, "maxLength": 500}, {"name": "Created", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "DateTime", "required": false, "readOnly": true, "systemField": true}, {"name": "Modified", "displayName": "Upravené", "type": "DateTime", "required": false, "readOnly": true, "systemField": true}, {"name": "Author", "displayName": "Autor", "type": "User", "required": false, "readOnly": true, "systemField": true}, {"name": "Editor", "displayName": "Editor", "type": "User", "required": false, "readOnly": true, "systemField": true}], "views": [{"name": "AllItems", "displayName": "Všetky položky", "default": true, "fields": ["Title", "Category", "Status", "DueDate", "AssignedTo", "Progress", "Modified"], "sortBy": "Modified", "sortOrder": "Descending", "filter": ""}, {"name": "ActiveItems", "displayName": "Aktívne <PERSON>", "fields": ["Title", "Category", "Status", "DueDate", "Progress"], "sortBy": "DueDate", "sortOrder": "Ascending", "filter": "Status ne 'Do<PERSON>čený' and Status ne 'Z<PERSON>š<PERSON>ý'"}, {"name": "HighPriority", "displayName": "Vysoká priorita", "fields": ["Title", "Status", "DueDate", "AssignedTo", "Progress"], "sortBy": "DueDate", "sortOrder": "Ascending", "filter": "Category eq 'Vysoká priorita'"}, {"name": "MyItems", "displayName": "<PERSON><PERSON>", "fields": ["Title", "Category", "Status", "DueDate", "Progress"], "sortBy": "DueDate", "sortOrder": "Ascending", "filter": "AssignedTo/Id eq @me"}]}], "permissions": {"read": ["Member", "Owner", "Visitor"], "write": ["Member", "Owner"], "delete": ["Owner"], "manage": ["Owner"]}, "caching": {"enabled": true, "duration": 300, "strategy": "LazyLoad"}}, {"name": "Office365Users", "displayName": "Office 365 Users", "type": "Office365Users", "version": "1.0", "description": "Pripojenie na Office 365 používateľov pre výber priradených osôb", "connectionParameters": {"authType": "Office365", "scopes": ["User.Read.All", "User.ReadBasic.All"]}, "operations": {"searchUser": {"enabled": true, "description": "Vyhľadávanie používateľov v organizácii"}, "getUserProfile": {"enabled": true, "description": "Získanie profilu používateľa"}, "getManager": {"enabled": false, "description": "Získanie manažéra používateľa"}}}], "connectionStrings": {"SharePoint": {"production": "https://{tenant}.sharepoint.com/sites/{production-site}", "development": "https://{tenant}.sharepoint.com/sites/{dev-site}", "testing": "https://{tenant}.sharepoint.com/sites/{test-site}"}}, "globalSettings": {"defaultTimeout": 30000, "maxRetries": 3, "enableCaching": true, "cacheTimeout": 300, "enableOfflineMode": false, "batchSize": 100, "enableDataLossPreventionPolicies": true}, "errorHandling": {"connectionErrors": {"retryPolicy": "exponentialBackoff", "maxRetries": 3, "baseDelay": 1000, "maxDelay": 10000}, "dataErrors": {"logErrors": true, "showUserMessages": true, "fallbackBehavior": "showCachedData"}}, "security": {"encryption": {"enabled": true, "algorithm": "AES-256"}, "authentication": {"required": true, "provider": "AzureAD", "sessionTimeout": 480}, "authorization": {"enableRoleBasedAccess": true, "defaultRole": "Member"}}, "monitoring": {"enableTelemetry": true, "logLevel": "Warning", "enablePerformanceCounters": true, "enableUsageAnalytics": true}}