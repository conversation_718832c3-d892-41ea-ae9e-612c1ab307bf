# PowerShell script na vytvorenie Dataverse Solution package
# Autor: Your Name
# Verzia: 1.0
# Datum: Januar 2024

param(
    [Parameter(Mandatory=$false)]
    [string]$OutputPath = "SharePointListManager_Solution.zip",

    [Parameter(Mandatory=$false)]
    [switch]$Force
)

Write-Host "Creating Dataverse Solution Package..." -ForegroundColor Green

# Kontrola existencie vystupneho suboru
if ((Test-Path $OutputPath) -and (-not $Force)) {
    Write-Host "File '$OutputPath' already exists. Use -Force parameter to overwrite." -ForegroundColor Yellow
    exit 0
} elseif ((Test-Path $OutputPath) -and $Force) {
    Write-Host "Removing existing file '$OutputPath'" -ForegroundColor Yellow
    Remove-Item $OutputPath -Force
}

# Zoznam suborov pre Dataverse Solution
$solutionFiles = @(
    "Content_Types.xml",
    "solution.xml",
    "customizations.xml",
    "_rels/.rels",
    "CanvasManifest.json",
    "Connections.json",
    "DataSources/PowerAppDataList.json",
    "DataSources/Office365Users.json",
    "Src/App.fx.yaml",
    "Src/BrowseScreen.fx.yaml",
    "Src/DetailsScreen.fx.yaml",
    "Src/EditScreen.fx.yaml",
    "Resources/PublishInfo.json",
    "pkgs/Wadl/Microsoft.PowerApps.CdsBaseDataSourceInfoProvider.xml",
    "Other/References.json",
    "Other/Entropy.json"
)

# Kontrola existencie vsetkych suborov
Write-Host "Checking file existence..." -ForegroundColor Yellow
$missingFiles = @()

foreach ($file in $solutionFiles) {
    if (-not (Test-Path $file)) {
        $missingFiles += $file
        Write-Host "Missing file: $file" -ForegroundColor Red
    } else {
        Write-Host "File exists: $file" -ForegroundColor Green
    }
}

if ($missingFiles.Count -gt 0) {
    Write-Host "`nMissing files:" -ForegroundColor Red
    $missingFiles | ForEach-Object { Write-Host "   - $_" -ForegroundColor Red }
    Write-Host "`nPlease create all required files first." -ForegroundColor Yellow
    exit 1
}

# Vytvorenie Dataverse Solution package
try {
    Write-Host "`nCreating Dataverse Solution package..." -ForegroundColor Yellow

    # Pouzitie .NET System.IO.Compression na vytvorenie ZIP
    Add-Type -AssemblyName System.IO.Compression.FileSystem

    # Vytvorenie docasneho adresara
    $tempDir = [System.IO.Path]::GetTempPath() + [System.Guid]::NewGuid().ToString()
    New-Item -ItemType Directory -Path $tempDir | Out-Null

    # Kopirovanie suborov do docasneho adresara so zachovanim struktury
    foreach ($file in $solutionFiles) {
        $sourceFile = $file

        # Specialne spracovanie pre Content_Types.xml -> [Content_Types].xml
        if ($file -eq "Content_Types.xml") {
            $targetFile = Join-Path $tempDir "[Content_Types].xml"
        } else {
            $targetFile = Join-Path $tempDir $file
        }

        $targetDir = Split-Path $targetFile -Parent

        # Vytvorenie adresara ak neexistuje
        if (-not (Test-Path $targetDir)) {
            New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
        }

        # Kopirovanie suboru
        Copy-Item $sourceFile $targetFile -Force
        Write-Host "Added: $file -> $(Split-Path $targetFile -Leaf)" -ForegroundColor Cyan
    }

    # Vytvorenie ZIP suboru
    [System.IO.Compression.ZipFile]::CreateFromDirectory($tempDir, $OutputPath)

    # Vycistenie docasneho adresara
    Remove-Item $tempDir -Recurse -Force

    Write-Host "Dataverse Solution package successfully created: $OutputPath" -ForegroundColor Green

} catch {
    Write-Host "Error creating Dataverse Solution package: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Informacie o vytvorenom subore
$solutionInfo = Get-Item $OutputPath
Write-Host "`nFile information:" -ForegroundColor Cyan
Write-Host "   Name: $($solutionInfo.Name)" -ForegroundColor White
Write-Host "   Size: $([math]::Round($solutionInfo.Length / 1KB, 2)) KB" -ForegroundColor White
Write-Host "   Created: $($solutionInfo.CreationTime)" -ForegroundColor White
Write-Host "   Path: $($solutionInfo.FullName)" -ForegroundColor White

# Instrukcie na import
Write-Host "`nNext steps to import Dataverse Solution:" -ForegroundColor Yellow
Write-Host "1. Go to https://make.powerapps.com/" -ForegroundColor White
Write-Host "2. Select correct Environment" -ForegroundColor White
Write-Host "3. Click on 'Solutions' in left menu" -ForegroundColor White
Write-Host "4. Click on 'Import solution'" -ForegroundColor White
Write-Host "5. Upload file: $OutputPath" -ForegroundColor White
Write-Host "6. Configure connection references for SharePoint and Office 365 Users" -ForegroundColor White
Write-Host "7. Set environment variables (SharePoint Site URL, List Name)" -ForegroundColor White
Write-Host "8. Click 'Import'" -ForegroundColor White

Write-Host "`nImportant notes:" -ForegroundColor Yellow
Write-Host "• Use 'Import solution' in Solutions area" -ForegroundColor White
Write-Host "• Create SharePoint list first using PowerShell_Setup.ps1" -ForegroundColor White
Write-Host "• Configure connection references during import" -ForegroundColor White
Write-Host "• Set environment variables for your SharePoint site" -ForegroundColor White
Write-Host "• You can add Power Automate flows to this solution later" -ForegroundColor White
Write-Host "• Check permissions on SharePoint list" -ForegroundColor White
Write-Host "• Test all functionality after import" -ForegroundColor White

Write-Host "`nDataverse Solution Package successfully created!" -ForegroundColor Green
Write-Host "This solution is ready for Power Automate flows integration!" -ForegroundColor Cyan
