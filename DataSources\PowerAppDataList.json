{"EntityName": "PowerAppDataList", "Type": "SharePointListEntity", "DisplayName": "PowerApp Data List", "LogicalName": "PowerApp Data List", "CollectionName": "PowerAppDataList", "EntitySetName": "PowerAppDataList", "IsHidden": false, "IsDisplayNameLocalized": false, "MinVersionToLoad": "1.331", "WadlMetadata": {"BaseUrl": "https://{tenant}.sharepoint.com/sites/{site}/_api/web/lists/getbytitle('PowerApp%20Data%20List')", "EntityContainer": {"Name": "PowerAppDataListContainer", "Extends": "Container"}, "EntitySet": {"Name": "PowerAppDataList", "EntityType": "PowerAppDataListEntity"}, "EntityType": {"Name": "PowerAppDataListEntity", "Key": {"PropertyRef": {"Name": "ID"}}, "Properties": [{"Name": "ID", "Type": "Edm.Int32", "Nullable": false, "ReadOnly": true}, {"Name": "Title", "Type": "Edm.String", "Nullable": false, "MaxLength": 255}, {"Name": "Description", "Type": "Edm.String", "Nullable": true}, {"Name": "Category", "Type": "Edm.String", "Nullable": false, "DefaultValue": "Stredná priorita"}, {"Name": "Status", "Type": "Edm.String", "Nullable": false, "DefaultValue": "Nový"}, {"Name": "DueDate", "Type": "Edm.DateTimeOffset", "Nullable": true}, {"Name": "AssignedTo", "Type": "SP.Data.UserInfoItem", "Nullable": true}, {"Name": "Progress", "Type": "Edm.<PERSON>", "Nullable": true, "DefaultValue": 0}, {"Name": "Tags", "Type": "Edm.String", "Nullable": true, "MaxLength": 500}, {"Name": "Created", "Type": "Edm.DateTimeOffset", "Nullable": false, "ReadOnly": true}, {"Name": "Modified", "Type": "Edm.DateTimeOffset", "Nullable": false, "ReadOnly": true}, {"Name": "Author", "Type": "SP.Data.UserInfoItem", "Nullable": false, "ReadOnly": true}, {"Name": "Editor", "Type": "SP.Data.UserInfoItem", "Nullable": false, "ReadOnly": true}]}}, "Schema": {"EntityContainer": "PowerAppDataListContainer", "EntitySet": "PowerAppDataList", "EntityType": "PowerAppDataListEntity"}, "Operations": {"Create": true, "Read": true, "Update": true, "Delete": true}, "TableDefinition": {"Name": "PowerAppDataList", "DisplayName": "PowerApp Data List", "Kind": "SharePointList", "Columns": [{"Name": "ID", "DisplayName": "ID", "Type": "Number", "IsPrimaryKey": true, "IsRequired": false, "IsReadOnly": true}, {"Name": "Title", "DisplayName": "N<PERSON>zov", "Type": "Text", "IsRequired": true, "MaxLength": 255}, {"Name": "Description", "DisplayName": "<PERSON><PERSON>", "Type": "Memo", "IsRequired": false}, {"Name": "Category", "DisplayName": "Kategória", "Type": "Choice", "IsRequired": true, "Choices": ["Vysoká priorita", "Stredná priorita", "Nízka priorita"], "DefaultValue": "Stredná priorita"}, {"Name": "Status", "DisplayName": "Stav", "Type": "Choice", "IsRequired": true, "Choices": ["Nový", "V riešení", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "DefaultValue": "Nový"}, {"Name": "DueDate", "DisplayName": "<PERSON><PERSON><PERSON>", "Type": "DateTime", "IsRequired": false, "Format": "DateOnly"}, {"Name": "AssignedTo", "DisplayName": "Priradené komu", "Type": "Lookup", "IsRequired": false, "LookupTable": "UserInfo"}, {"Name": "Progress", "DisplayName": "Pokrok (%)", "Type": "Number", "IsRequired": false, "Min": 0, "Max": 100, "DefaultValue": 0}, {"Name": "Tags", "DisplayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "Text", "IsRequired": false, "MaxLength": 500}, {"Name": "Created", "DisplayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "DateTime", "IsRequired": false, "IsReadOnly": true}, {"Name": "Modified", "DisplayName": "Upravené", "Type": "DateTime", "IsRequired": false, "IsReadOnly": true}, {"Name": "Author", "DisplayName": "Autor", "Type": "Lookup", "IsRequired": false, "IsReadOnly": true, "LookupTable": "UserInfo"}, {"Name": "Editor", "DisplayName": "Editor", "Type": "Lookup", "IsRequired": false, "IsReadOnly": true, "LookupTable": "UserInfo"}]}}