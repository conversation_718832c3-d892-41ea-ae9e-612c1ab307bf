BrowseScreen As screen:
    Fill: =RGBA(248, 249, 250, 1)
    OnVisible: |-
        =Set(varSearchText, "");
        Set(varCate<PERSON>y<PERSON>ilter, Blank());
        Set(varStatus<PERSON>ilter, Blank())

    HeaderContainer As groupContainer.verticalAutoLayoutContainer:
        Height: =80
        LayoutDirection: =LayoutDirection.Vertical
        LayoutGap: =0
        LayoutMode: =LayoutMode.Auto
        RadiusBottomLeft: =0
        RadiusBottomRight: =0
        RadiusTopLeft: =0
        RadiusTopRight: =0
        Width: =Parent.Width
        X: =0
        Y: =0
        ZIndex: =1

        AppTitle As label:
            Align: =Align.Left
            Color: =RGBA(255, 255, 255, 1)
            FontWeight: =FontWeight.Bold
            Height: =40
            Size: =18
            Text: ="SharePoint List Manager"
            Width: =300
            X: =20
            Y: =20
            ZIndex: =1

        RefreshButton As button:
            BorderColor: =RGBA(255, 255, 255, 1)
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(255, 255, 255, 0.2)
            Height: =30
            OnSelect: |-
                =Refresh(PowerAppDataList);
                Notify("Dáta boli obnovené", NotificationType.Success)
            Text: ="Obnoviť"
            Width: =100
            X: =Parent.Width - 120
            Y: =25
            ZIndex: =2

    SearchContainer As groupContainer.verticalAutoLayoutContainer:
        Fill: =RGBA(248, 249, 250, 1)
        Height: =60
        LayoutDirection: =LayoutDirection.Vertical
        LayoutGap: =0
        LayoutMode: =LayoutMode.Auto
        Width: =Parent.Width
        X: =0
        Y: =80
        ZIndex: =2

        SearchInput As text:
            BorderColor: =RGBA(206, 212, 218, 1)
            Default: =""
            Height: =30
            HintText: ="Hľadať v názve alebo popise..."
            OnChange: =Set(varSearchText, SearchInput.Text)
            Width: =Parent.Width - 160
            X: =20
            Y: =15
            ZIndex: =1

        AddButton As button:
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(40, 167, 69, 1)
            Height: =30
            OnSelect: |-
                =Set(varSelectedItem, Blank());
                Navigate(EditScreen, ScreenTransition.Fade)
            Text: ="+ Pridať"
            Width: =100
            X: =Parent.Width - 120
            Y: =15
            ZIndex: =2

    FilterContainer As groupContainer.verticalAutoLayoutContainer:
        Fill: =RGBA(255, 255, 255, 1)
        Height: =50
        LayoutDirection: =LayoutDirection.Vertical
        LayoutGap: =0
        LayoutMode: =LayoutMode.Auto
        Width: =Parent.Width
        X: =0
        Y: =140
        ZIndex: =3

        CategoryFilter As dropdown:
            DefaultSelectedItems: =[]
            Height: =30
            HintText: ="Filtrovať podľa kategórie"
            Items: =Distinct(PowerAppDataList, Category)
            OnChange: =Set(varCategoryFilter, CategoryFilter.Selected.Value)
            Width: =200
            X: =20
            Y: =10
            ZIndex: =1

        StatusFilter As dropdown:
            DefaultSelectedItems: =[]
            Height: =30
            HintText: ="Filtrovať podľa stavu"
            Items: =Distinct(PowerAppDataList, Status)
            OnChange: =Set(varStatusFilter, StatusFilter.Selected.Value)
            Width: =200
            X: =240
            Y: =10
            ZIndex: =2

        ClearFiltersButton As button:
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(108, 117, 125, 1)
            Height: =30
            OnSelect: |-
                =Reset(CategoryFilter);
                Reset(StatusFilter);
                Set(varCategoryFilter, Blank());
                Set(varStatusFilter, Blank());
                Set(varSearchText, "");
                Reset(SearchInput)
            Text: ="Vymazať filtre"
            Width: =120
            X: =460
            Y: =10
            ZIndex: =3

    ItemsGallery As gallery.BrowseLayout_Vertical_TwoTextOneImageVariant_ver5.0:
        BorderColor: =RGBA(222, 226, 230, 1)
        BorderThickness: =1
        Height: =Parent.Height - 190
        Items: |-
            =Filter(
                PowerAppDataList,
                (IsBlank(varSearchText) || varSearchText in Title || varSearchText in Description) &&
                (IsBlank(varCategoryFilter) || Category = varCategoryFilter) &&
                (IsBlank(varStatusFilter) || Status = varStatusFilter)
            )
        Layout: =Layout.Vertical
        OnSelect: |-
            =Set(varSelectedItem, ThisItem);
            Navigate(DetailsScreen, ScreenTransition.Fade)
        TemplateSize: =120
        Width: =Parent.Width
        X: =0
        Y: =190
        ZIndex: =4

        TitleLabel As label:
            Color: =RGBA(33, 37, 41, 1)
            FontWeight: =FontWeight.Bold
            Height: =25
            Size: =14
            Text: =ThisItem.Title
            Width: =Parent.TemplateWidth - 150
            X: =15
            Y: =10
            ZIndex: =1

        DescriptionLabel As label:
            Color: =RGBA(108, 117, 125, 1)
            Height: =40
            Size: =11
            Text: =Left(ThisItem.Description, 100) & If(Len(ThisItem.Description) > 100, "...", "")
            Width: =Parent.TemplateWidth - 150
            X: =15
            Y: =35
            ZIndex: =2

        CategoryLabel As label:
            Align: =Align.Center
            BorderRadius: =12
            Color: =RGBA(255, 255, 255, 1)
            Fill: |-
                =Switch(
                    ThisItem.Category,
                    "Vysoká priorita", RGBA(220, 53, 69, 1),
                    "Stredná priorita", RGBA(255, 193, 7, 1),
                    "Nízka priorita", RGBA(40, 167, 69, 1),
                    RGBA(108, 117, 125, 1)
                )
            Height: =20
            Size: =10
            Text: =ThisItem.Category
            Width: =120
            X: =15
            Y: =80
            ZIndex: =3

        StatusLabel As label:
            Align: =Align.Center
            Color: |-
                =Switch(
                    ThisItem.Status,
                    "Dokončený", RGBA(40, 167, 69, 1),
                    "V riešení", RGBA(0, 120, 212, 1),
                    "Nový", RGBA(255, 193, 7, 1),
                    "Zrušený", RGBA(220, 53, 69, 1),
                    RGBA(108, 117, 125, 1)
                )
            FontWeight: =FontWeight.Semibold
            Height: =25
            Size: =11
            Text: =ThisItem.Status
            Width: =100
            X: =Parent.TemplateWidth - 120
            Y: =10
            ZIndex: =4

        DueDateLabel As label:
            Align: =Align.Center
            Color: |-
                =If(
                    ThisItem.DueDate < Today() && ThisItem.Status <> "Dokončený",
                    RGBA(220, 53, 69, 1),
                    RGBA(108, 117, 125, 1)
                )
            Height: =20
            Size: =10
            Text: =If(IsBlank(ThisItem.DueDate), "Bez termínu", Text(ThisItem.DueDate, "dd.mm.yyyy"))
            Width: =100
            X: =Parent.TemplateWidth - 120
            Y: =40
            ZIndex: =5

        ProgressBarBackground As rectangle:
            BorderThickness: =0
            Fill: =RGBA(222, 226, 230, 1)
            Height: =4
            Width: =100
            X: =Parent.TemplateWidth - 120
            Y: =70
            ZIndex: =6

        ProgressBar As rectangle:
            BorderThickness: =0
            Fill: =RGBA(0, 120, 212, 1)
            Height: =4
            Width: =100 * (ThisItem.Progress / 100)
            X: =Parent.TemplateWidth - 120
            Y: =70
            ZIndex: =7
