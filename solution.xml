<?xml version="1.0" encoding="utf-8"?>
<ImportExportXml version="9.2.24021.00227" SolutionPackageVersion="9.2" languagecode="1033" generatedBy="CrmLive" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <SolutionManifest>
    <UniqueName>SharePointListManager</UniqueName>
    <LocalizedNames>
      <LocalizedName description="SharePoint List Manager Solution" languagecode="1033" />
      <LocalizedName description="SharePoint List Manager Solution" languagecode="1051" />
    </LocalizedNames>
    <Descriptions>
      <Description description="Complete solution for SharePoint list management with Canvas PowerApp and future Power Automate flows" languagecode="1033" />
      <Description description="Kompletné riešenie pre správu SharePoint listu s Canvas PowerApp a budúcimi Power Automate flow" languagecode="1051" />
    </Descriptions>
    <Version>*******</Version>
    <Managed>0</Managed>
    <Publisher>
      <UniqueName>DefaultPublisher</UniqueName>
      <LocalizedNames>
        <LocalizedName description="Default Publisher" languagecode="1033" />
      </LocalizedNames>
      <Descriptions>
        <Description description="Default Publisher for SharePoint List Manager Solution" languagecode="1033" />
      </Descriptions>
      <EMailAddress><EMAIL></EMailAddress>
      <SupportingWebsiteUrl>https://company.com</SupportingWebsiteUrl>
      <CustomizationPrefix>new</CustomizationPrefix>
      <CustomizationOptionValuePrefix>10000</CustomizationOptionValuePrefix>
      <Addresses>
        <Address>
          <AddressNumber>1</AddressNumber>
          <AddressTypeCode>1</AddressTypeCode>
          <City>Bratislava</City>
          <County>Bratislava</County>
          <Country>Slovakia</Country>
          <PostalCode>81101</PostalCode>
          <StateOrProvince>Bratislava</StateOrProvince>
          <Telephone1>+421 2 1234 5678</Telephone1>
        </Address>
      </Addresses>
    </Publisher>
    <RootComponents>
      <RootComponent type="300" schemaName="new_sharepointlistmanager" behavior="0" />
      <RootComponent type="381" schemaName="new_sharepoint" behavior="0" />
      <RootComponent type="381" schemaName="new_office365users" behavior="0" />
      <RootComponent type="380" schemaName="new_SharePointSiteUrl" behavior="0" />
      <RootComponent type="380" schemaName="new_SharePointListName" behavior="0" />
    </RootComponents>
    <MissingDependencies />
  </SolutionManifest>
</ImportExportXml>
