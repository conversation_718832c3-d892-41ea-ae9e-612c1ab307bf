App As appinfo:
    BackEnabled: =false
    OnStart: |-
        =// Inicializácia globálnych premenných
        Set(gblCurrentUser, User());
        Set(gblToday, Today());
        Set(gblAppVersion, "1.0.0");

        // Definícia farieb témy
        Set(gblThemeColors, {
            Primary: RGBA(0, 120, 212, 1),
            Secondary: RGBA(108, 117, 125, 1),
            Success: RGBA(40, 167, 69, 1),
            Warning: RGBA(255, 193, 7, 1),
            Danger: RGBA(220, 53, 69, 1),
            Light: RGBA(248, 249, 250, 1),
            Dark: RGBA(33, 37, 41, 1)
        });

        // Kolekcie pre dropdown hodnoty
        ClearCollect(colCategories, ["Vysoká priorita", "Stredná priorita", "Nízka priorita"]);
        ClearCollect(colStatuses, ["<PERSON><PERSON>", "<PERSON> rie<PERSON>ení", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]);

        // Inicializácia premenných
        Set(varSelectedItem, Blank());
        Set(varSearchText, "");
        Set(varCategoryFilter, Blank());
        Set(varStatusFilter, Blank())
    StartScreen: =BrowseScreen
    Theme: =PowerAppsTheme
