DetailsScreen As screen:
    Fill: =RGBA(248, 249, 250, 1)
    OnVisible: =If(IsBlank(varSelectedItem), Navigate(BrowseScreen, ScreenTransition.Fade))

    HeaderContainer As groupContainer.verticalAutoLayoutContainer:
        Fill: =RGBA(0, 120, 212, 1)
        Height: =80
        LayoutDirection: =LayoutDirection.Vertical
        LayoutGap: =0
        LayoutMode: =LayoutMode.Auto
        RadiusBottomLeft: =0
        RadiusBottomRight: =0
        RadiusTopLeft: =0
        RadiusTopRight: =0
        Width: =Parent.Width
        X: =0
        Y: =0
        ZIndex: =1

        BackButton As button:
            BorderColor: =RGBA(255, 255, 255, 1)
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(255, 255, 255, 0.2)
            Height: =30
            OnSelect: =Navigate(BrowseScreen, ScreenTransition.Fade)
            Text: ="← Späť"
            Width: =80
            X: =20
            Y: =25
            ZIndex: =1

        ScreenTitle As label:
            Align: =Align.Left
            Color: =RGBA(255, 255, 255, 1)
            FontWeight: =FontWeight.Bold
            Height: =40
            Size: =18
            Text: ="Detail položky"
            Width: =200
            X: =120
            Y: =20
            ZIndex: =2

        EditButton As button:
            Color: =RGBA(33, 37, 41, 1)
            Fill: =RGBA(255, 193, 7, 1)
            Height: =30
            OnSelect: =Navigate(EditScreen, ScreenTransition.Fade)
            Text: ="Upraviť"
            Width: =100
            X: =Parent.Width - 120
            Y: =25
            ZIndex: =3

    ContentContainer As groupContainer.verticalAutoLayoutContainer:
        Fill: =RGBA(248, 249, 250, 1)
        Height: =Parent.Height - 90
        LayoutDirection: =LayoutDirection.Vertical
        LayoutGap: =20
        LayoutMode: =LayoutMode.Auto
        PaddingBottom: =20
        PaddingLeft: =20
        PaddingRight: =20
        PaddingTop: =20
        Width: =Parent.Width
        X: =0
        Y: =90
        ZIndex: =2

        TitleSection As groupContainer.verticalAutoLayoutContainer:
            BorderColor: =RGBA(222, 226, 230, 1)
            BorderRadius: =8
            BorderThickness: =1
            Fill: =RGBA(255, 255, 255, 1)
            Height: =80
            LayoutDirection: =LayoutDirection.Vertical
            LayoutGap: =0
            LayoutMode: =LayoutMode.Auto
            PaddingBottom: =16
            PaddingLeft: =16
            PaddingRight: =16
            PaddingTop: =16
            Width: =Parent.Width
            ZIndex: =1

            TitleLabel As label:
                Color: =RGBA(33, 37, 41, 1)
                FontWeight: =FontWeight.Bold
                Height: =30
                Size: =20
                Text: =varSelectedItem.Title
                Width: =Parent.Width
                X: =0
                Y: =0
                ZIndex: =1

            CreatedInfo As label:
                Color: =RGBA(108, 117, 125, 1)
                Height: =25
                Size: =11
                Text: ="Vytvorené: " & Text(varSelectedItem.Created, "dd.mm.yyyy hh:mm") & " | Autor: " & varSelectedItem.Author.DisplayName
                Width: =Parent.Width
                X: =0
                Y: =35
                ZIndex: =2

        StatusSection As groupContainer.verticalAutoLayoutContainer:
            BorderColor: =RGBA(222, 226, 230, 1)
            BorderRadius: =8
            BorderThickness: =1
            Fill: =RGBA(255, 255, 255, 1)
            Height: =120
            LayoutDirection: =LayoutDirection.Vertical
            LayoutGap: =0
            LayoutMode: =LayoutMode.Auto
            PaddingBottom: =16
            PaddingLeft: =16
            PaddingRight: =16
            PaddingTop: =16
            Width: =Parent.Width
            ZIndex: =2

            StatusContainer As groupContainer.horizontalAutoLayoutContainer:
                Height: =40
                LayoutDirection: =LayoutDirection.Horizontal
                LayoutGap: =20
                LayoutMode: =LayoutMode.Auto
                Width: =Parent.Width
                ZIndex: =1

                StatusLabelTitle As label:
                    Color: =RGBA(33, 37, 41, 1)
                    FontWeight: =FontWeight.Semibold
                    Height: =30
                    Size: =12
                    Text: ="Stav:"
                    Width: =80
                    X: =0
                    Y: =0
                    ZIndex: =1

                StatusBadge As label:
                    Align: =Align.Center
                    BorderRadius: =12
                    Color: =RGBA(255, 255, 255, 1)
                    Fill: |-
                        =Switch(
                            varSelectedItem.Status,
                            "Dokončený", RGBA(40, 167, 69, 1),
                            "V riešení", RGBA(0, 120, 212, 1),
                            "Nový", RGBA(255, 193, 7, 1),
                            "Zrušený", RGBA(220, 53, 69, 1),
                            RGBA(108, 117, 125, 1)
                        )
                    Height: =25
                    Size: =11
                    Text: =varSelectedItem.Status
                    Width: =120
                    X: =90
                    Y: =5
                    ZIndex: =2

                CategoryLabelTitle As label:
                    Color: =RGBA(33, 37, 41, 1)
                    FontWeight: =FontWeight.Semibold
                    Height: =30
                    Size: =12
                    Text: ="Kategória:"
                    Width: =80
                    X: =230
                    Y: =0
                    ZIndex: =3

                CategoryBadge As label:
                    Align: =Align.Center
                    BorderRadius: =12
                    Color: =RGBA(255, 255, 255, 1)
                    Fill: |-
                        =Switch(
                            varSelectedItem.Category,
                            "Vysoká priorita", RGBA(220, 53, 69, 1),
                            "Stredná priorita", RGBA(255, 193, 7, 1),
                            "Nízka priorita", RGBA(40, 167, 69, 1),
                            RGBA(108, 117, 125, 1)
                        )
                    Height: =25
                    Size: =11
                    Text: =varSelectedItem.Category
                    Width: =120
                    X: =320
                    Y: =5
                    ZIndex: =4

            ProgressContainer As groupContainer.verticalAutoLayoutContainer:
                Height: =60
                LayoutDirection: =LayoutDirection.Vertical
                LayoutGap: =5
                LayoutMode: =LayoutMode.Auto
                Width: =Parent.Width
                Y: =50
                ZIndex: =2

                ProgressHeader As groupContainer.horizontalAutoLayoutContainer:
                    Height: =25
                    LayoutDirection: =LayoutDirection.Horizontal
                    LayoutGap: =10
                    LayoutMode: =LayoutMode.Auto
                    Width: =Parent.Width
                    ZIndex: =1

                    ProgressLabelTitle As label:
                        Color: =RGBA(33, 37, 41, 1)
                        FontWeight: =FontWeight.Semibold
                        Height: =25
                        Size: =12
                        Text: ="Pokrok:"
                        Width: =80
                        X: =0
                        Y: =0
                        ZIndex: =1

                    ProgressValue As label:
                        Color: =RGBA(33, 37, 41, 1)
                        Height: =25
                        Size: =12
                        Text: =varSelectedItem.Progress & "%"
                        Width: =60
                        X: =90
                        Y: =0
                        ZIndex: =2

                ProgressBarBackground As rectangle:
                    BorderRadius: =4
                    BorderThickness: =0
                    Fill: =RGBA(222, 226, 230, 1)
                    Height: =8
                    Width: =Parent.Width - 20
                    X: =0
                    Y: =30
                    ZIndex: =2

                ProgressBarFill As rectangle:
                    BorderRadius: =4
                    BorderThickness: =0
                    Fill: =RGBA(0, 120, 212, 1)
                    Height: =8
                    Width: =(Parent.Width - 20) * (varSelectedItem.Progress / 100)
                    X: =0
                    Y: =30
                    ZIndex: =3

        DetailsSection As groupContainer.verticalAutoLayoutContainer:
            BorderColor: =RGBA(222, 226, 230, 1)
            BorderRadius: =8
            BorderThickness: =1
            Fill: =RGBA(255, 255, 255, 1)
            Height: =200
            LayoutDirection: =LayoutDirection.Vertical
            LayoutGap: =10
            LayoutMode: =LayoutMode.Auto
            PaddingBottom: =16
            PaddingLeft: =16
            PaddingRight: =16
            PaddingTop: =16
            Width: =Parent.Width
            ZIndex: =3

            DescriptionTitle As label:
                Color: =RGBA(33, 37, 41, 1)
                FontWeight: =FontWeight.Semibold
                Height: =25
                Size: =12
                Text: ="Popis:"
                Width: =100
                X: =0
                Y: =0
                ZIndex: =1

            DescriptionText As label:
                AutoHeight: =true
                Color: =RGBA(33, 37, 41, 1)
                Height: =100
                Size: =11
                Text: =If(IsBlank(varSelectedItem.Description), "Bez popisu", varSelectedItem.Description)
                Width: =Parent.Width - 20
                X: =0
                Y: =30
                ZIndex: =2

            DueDateTitle As label:
                Color: =RGBA(33, 37, 41, 1)
                FontWeight: =FontWeight.Semibold
                Height: =25
                Size: =12
                Text: ="Termín dokončenia:"
                Width: =150
                X: =0
                Y: =140
                ZIndex: =3

            DueDateValue As label:
                Color: =If(varSelectedItem.DueDate < Today() && varSelectedItem.Status <> "Dokončený", RGBA(220, 53, 69, 1), RGBA(33, 37, 41, 1))
                Height: =25
                Size: =11
                Text: =If(IsBlank(varSelectedItem.DueDate), "Nie je stanovený", Text(varSelectedItem.DueDate, "dd.mm.yyyy"))
                Width: =200
                X: =160
                Y: =140
                ZIndex: =4

            AssignedToTitle As label:
                Color: =RGBA(33, 37, 41, 1)
                FontWeight: =FontWeight.Semibold
                Height: =25
                Size: =12
                Text: ="Priradené komu:"
                Width: =150
                X: =0
                Y: =170
                ZIndex: =5

            AssignedToValue As label:
                Color: =RGBA(33, 37, 41, 1)
                Height: =25
                Size: =11
                Text: =If(IsBlank(varSelectedItem.AssignedTo), "Nie je priradené", varSelectedItem.AssignedTo.DisplayName)
                Width: =200
                X: =160
                Y: =170
                ZIndex: =6

        TagsSection As groupContainer.verticalAutoLayoutContainer:
            BorderColor: =RGBA(222, 226, 230, 1)
            BorderRadius: =8
            BorderThickness: =1
            Fill: =RGBA(255, 255, 255, 1)
            Height: =80
            LayoutDirection: =LayoutDirection.Vertical
            LayoutGap: =5
            LayoutMode: =LayoutMode.Auto
            PaddingBottom: =16
            PaddingLeft: =16
            PaddingRight: =16
            PaddingTop: =16
            Width: =Parent.Width
            ZIndex: =4

            TagsTitle As label:
                Color: =RGBA(33, 37, 41, 1)
                FontWeight: =FontWeight.Semibold
                Height: =25
                Size: =12
                Text: ="Štítky:"
                Width: =100
                X: =0
                Y: =0
                ZIndex: =1

            TagsValue As label:
                AutoHeight: =true
                Color: =RGBA(33, 37, 41, 1)
                Height: =40
                Size: =11
                Text: =If(IsBlank(varSelectedItem.Tags), "Žiadne štítky", varSelectedItem.Tags)
                Width: =Parent.Width - 20
                X: =0
                Y: =30
                ZIndex: =2
