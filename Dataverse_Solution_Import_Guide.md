# 📦 Dataverse Solution Import Guide

Tento návod vás prevedie importom Dataverse solution package do PowerApps prostredia cez **Solutions** sekciu.

## 🎯 Pred importom

### 1. Vytvorenie SharePoint listu

**Automaticky (Odporúčané):**
```powershell
.\PowerShell_Setup.ps1 -SiteUrl "https://yourtenant.sharepoint.com/sites/yoursite"
```

### 2. Príprava solution package

Solution package `SharePointListManager_Solution.zip` (16.91 KB) je už pripravený na import.

## 📥 Import do PowerApps Solutions

### Krok 1: Prístup do PowerApps Solutions

1. Otvorte [https://make.powerapps.com/](https://make.powerapps.com/)
2. Prihláste sa pomocou vášho Office 365 účtu
3. Vyberte správne **Environment** (prostredie)
4. **Kliknite na "Solutions"** v ľavom menu (nie Apps!)

### Krok 2: Import solution

1. **Kliknite na "Import solution"**
2. **Nahrajte ZIP súbor:**
   - Kliknite na "Browse"
   - <PERSON><PERSON><PERSON>e `SharePointListManager_Solution.zip`
   - Kliknite na "Next"

### Krok 3: Konfigurácia solution

1. **Základné informácie:**
   ```
   Name: SharePoint List Manager Solution
   Version: *******
   Publisher: Default Publisher
   ```

2. **Kliknite na "Next"**

### Krok 4: Konfigurácia Connection References

**SharePoint Connection:**
1. Kliknite na dropdown pri "new_sharepoint"
2. Vyberte existujúce pripojenie alebo "Create new"
3. Ak vytvárate nové:
   - Name: "SharePoint Connection"
   - Autorizujte pripojenie k SharePoint
   - Kliknite "Create"

**Office 365 Users Connection:**
1. Kliknite na dropdown pri "new_office365users"
2. Vyberte existujúce pripojenie alebo "Create new"
3. Ak vytvárate nové:
   - Name: "Office 365 Users Connection"
   - Autorizujte pripojenie
   - Kliknite "Create"

### Krok 5: Nastavenie Environment Variables

**SharePoint Site URL:**
1. Kliknite na "new_SharePointSiteUrl"
2. Zadajte hodnotu:
   ```
   https://yourtenant.sharepoint.com/sites/yoursite
   ```

**SharePoint List Name:**
1. Kliknite na "new_SharePointListName"
2. Zadajte hodnotu:
   ```
   PowerApp Data List
   ```

### Krok 6: Dokončenie importu

1. **Skontrolujte všetky nastavenia**
2. **Kliknite na "Import"**
3. **Počkajte na dokončenie** (môže trvať 2-5 minút)

## ✅ Po úspešnom importe

### 1. Overenie importu

1. **Prejdite do Solutions**
2. **Nájdite "SharePoint List Manager Solution"**
3. **Kliknite na solution** pre zobrazenie obsahu
4. **Skontrolujte komponenty:**
   - ✅ Canvas app: "SharePoint List Manager"
   - ✅ Connection references: SharePoint, Office 365 Users
   - ✅ Environment variables: Site URL, List Name

### 2. Testovanie aplikácie

1. **Otvorte Canvas app** v solution
2. **Kliknite na "Play"** alebo "Edit"
3. **Otestujte funkcionality:**
   - ✅ Načítanie dát zo SharePoint listu
   - ✅ Vyhľadávanie a filtrovanie
   - ✅ Vytvorenie novej položky
   - ✅ Úprava existujúcej položky
   - ✅ Mazanie položky

## 🔧 Riešenie problémov

### Problém: Connection references sa nedajú nastaviť

**Riešenie:**
1. Skontrolujte oprávnenia na SharePoint site
2. Overte, že máte prístup k Office 365 Users API
3. Skúste vytvoriť pripojenia manuálne pred importom

### Problém: Environment variables sa nedajú nastaviť

**Riešenie:**
1. Skontrolujte, či máte oprávnenia na úpravu environment variables
2. Overte správnosť SharePoint URL
3. Skontrolujte existenciu SharePoint listu

### Problém: Import zlyhá

**Riešenie:**
1. Skontrolujte, či je environment Dataverse-enabled
2. Overte, že máte System Administrator alebo System Customizer role
3. Skúste import v inom environment

## 🚀 Pridanie Power Automate Flows

### Výhody Dataverse solution:

1. **Centralizovaná správa** - Všetky komponenty v jednom solution
2. **Verzovanie** - Sledovanie zmien a aktualizácií
3. **Deployment** - Jednoduchý presun medzi environments
4. **Dependencies** - Automatické riadenie závislostí

### Pridanie Power Automate flow:

1. **Otvorte solution** v PowerApps
2. **Kliknite na "New" → "Automation" → "Cloud flow"**
3. **Vytvorte flow** s SharePoint triggermi/akciami
4. **Flow sa automaticky pridá** do solution

### Príklady užitočných flows:

**1. Notifikácie pri vytvorení položky:**
```
Trigger: When an item is created (SharePoint)
Action: Send email notification (Office 365 Outlook)
```

**2. Automatické priradenie na základe kategórie:**
```
Trigger: When an item is created or modified (SharePoint)
Condition: If Category = "Vysoká priorita"
Action: Update item - assign to manager
```

**3. Eskalácia po termíne:**
```
Trigger: Recurrence (daily)
Action: Get items where DueDate < Today and Status ≠ "Dokončený"
Action: Send reminder emails
```

**4. Synchronizácia s Planner:**
```
Trigger: When an item is created (SharePoint)
Action: Create task in Planner
```

## 📊 Monitoring a správa

### Solution Health:

1. **PowerApps Admin Center** → **Analytics**
2. **Solution Checker** - Analýza kvality
3. **Usage Reports** - Štatistiky používania

### Aktualizácie:

1. **Export solution** ako managed
2. **Úpravy v dev environment**
3. **Import do production** ako upgrade

### Backup:

1. **Export solution** ako unmanaged
2. **Uloženie ZIP súboru**
3. **Dokumentácia zmien**

## 🎯 Best Practices

### Pre Solutions:

1. **Používajte semantic versioning** (1.0.0 → 1.0.1 → 1.1.0)
2. **Vytvorte separate dev/test/prod environments**
3. **Používajte managed solutions pre production**
4. **Dokumentujte všetky zmeny**

### Pre Power Automate:

1. **Používajte connection references** namiesto direct connections
2. **Nastavte error handling** vo flows
3. **Používajte environment variables** pre konfiguráciu
4. **Testujte flows pred produkčným nasadením**

## 📞 Podpora

### Kontakty:
- **IT Helpdesk**: [<EMAIL>]
- **PowerApps Admin**: [<EMAIL>]
- **SharePoint Admin**: [<EMAIL>]

### Užitočné odkazy:
- [Solutions Documentation](https://docs.microsoft.com/en-us/power-platform/alm/)
- [Power Automate Documentation](https://docs.microsoft.com/en-us/power-automate/)
- [Environment Variables](https://docs.microsoft.com/en-us/power-apps/maker/data-platform/environmentvariables)

## ✅ Checklist po importe

- [ ] Solution úspešne importovaný
- [ ] Connection references nakonfigurované
- [ ] Environment variables nastavené
- [ ] Canvas app funguje správne
- [ ] SharePoint list pripojený
- [ ] CRUD operácie testované
- [ ] Oprávnenia skontrolované
- [ ] Dokumentácia poskytnutá
- [ ] Používatelia informovaní
- [ ] Backup vytvorený

---

**Verzia:** 1.0  
**Dátum:** 26. mája 2025  
**Autor:** Augment Agent  
**Solution Package:** SharePointListManager_Solution.zip (16.91 KB)
