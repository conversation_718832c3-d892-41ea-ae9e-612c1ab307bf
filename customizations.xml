<?xml version="1.0" encoding="utf-8"?>
<ImportExportXml version="9.2.24021.00227" SolutionPackageVersion="9.2" languagecode="1033" generatedBy="CrmLive" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <SolutionManifest>
    <UniqueName>SharePointListManager</UniqueName>
    <LocalizedNames>
      <LocalizedName description="SharePoint List Manager Solution" languagecode="1033" />
      <LocalizedName description="SharePoint List Manager Solution" languagecode="1051" />
    </LocalizedNames>
    <Descriptions>
      <Description description="Complete solution for SharePoint list management with Canvas PowerApp and future Power Automate flows" languagecode="1033" />
      <Description description="Kompletné riešenie pre správu SharePoint listu s Canvas PowerApp a budúcimi Power Automate flow" languagecode="1051" />
    </Descriptions>
    <Version>*******</Version>
    <Managed>0</Managed>
    <Publisher>
      <UniqueName>DefaultPublisher</UniqueName>
      <LocalizedNames>
        <LocalizedName description="Default Publisher" languagecode="1033" />
      </LocalizedNames>
      <Descriptions>
        <Description description="Default Publisher for SharePoint List Manager Solution" languagecode="1033" />
      </Descriptions>
      <EMailAddress><EMAIL></EMailAddress>
      <SupportingWebsiteUrl>https://company.com</SupportingWebsiteUrl>
      <CustomizationPrefix>new</CustomizationPrefix>
      <CustomizationOptionValuePrefix>10000</CustomizationOptionValuePrefix>
      <Addresses>
        <Address>
          <AddressNumber>1</AddressNumber>
          <AddressTypeCode>1</AddressTypeCode>
          <City>Bratislava</City>
          <County>Bratislava</County>
          <Country>Slovakia</Country>
          <PostalCode>81101</PostalCode>
          <StateOrProvince>Bratislava</StateOrProvince>
          <Telephone1>+421 2 1234 5678</Telephone1>
        </Address>
      </Addresses>
    </Publisher>
    <RootComponents>
      <RootComponent type="300" schemaName="new_sharepointlistmanager" behavior="0" />
    </RootComponents>
    <MissingDependencies />
  </SolutionManifest>
  
  <canvasapps>
    <canvasapp schemaName="new_sharepointlistmanager">
      <displayname>SharePoint List Manager</displayname>
      <description>Canvas PowerApp pre správu SharePoint listu s plnou CRUD funkcionalitou</description>
      <commitMessage>Initial version of SharePoint List Manager</commitMessage>
      <publisher>Default Publisher</publisher>
      <authorizationReferences>
        <authorizationReference id="shared_sharepointonline">
          <displayName>SharePoint</displayName>
          <connectionParameters>
            <connectionParameter name="token">
              <value></value>
            </connectionParameter>
            <connectionParameter name="token:TenantId">
              <value></value>
            </connectionParameter>
          </connectionParameters>
        </authorizationReference>
        <authorizationReference id="shared_office365users">
          <displayName>Office 365 Users</displayName>
          <connectionParameters>
            <connectionParameter name="token">
              <value></value>
            </connectionParameter>
            <connectionParameter name="token:TenantId">
              <value></value>
            </connectionParameter>
          </connectionParameters>
        </authorizationReference>
      </authorizationReferences>
      <databaseReferences />
      <appComponents>
        <appComponent>
          <name>App</name>
          <type>App</type>
          <content>Src/App.fx.yaml</content>
        </appComponent>
        <appComponent>
          <name>BrowseScreen</name>
          <type>Screen</type>
          <content>Src/BrowseScreen.fx.yaml</content>
        </appComponent>
        <appComponent>
          <name>DetailsScreen</name>
          <type>Screen</type>
          <content>Src/DetailsScreen.fx.yaml</content>
        </appComponent>
        <appComponent>
          <name>EditScreen</name>
          <type>Screen</type>
          <content>Src/EditScreen.fx.yaml</content>
        </appComponent>
      </appComponents>
      <dataSources>
        <dataSource>
          <name>PowerAppDataList</name>
          <type>SharePointList</type>
          <content>DataSources/PowerAppDataList.json</content>
        </dataSource>
        <dataSource>
          <name>Office365Users</name>
          <type>Office365Users</type>
          <content>DataSources/Office365Users.json</content>
        </dataSource>
      </dataSources>
      <resources>
        <resource>
          <name>PublishInfo</name>
          <type>PublishInfo</type>
          <content>Resources/PublishInfo.json</content>
        </resource>
      </resources>
    </canvasapp>
  </canvasapps>
  
  <workflows />
  <connectionreferences>
    <connectionreference connectionreferencelogicalname="new_sharepoint">
      <connectionreferencedisplayname>SharePoint Connection</connectionreferencedisplayname>
      <connectorid>/providers/Microsoft.PowerApps/apis/shared_sharepointonline</connectorid>
      <iscustomizable>1</iscustomizable>
      <statecode>0</statecode>
    </connectionreference>
    <connectionreference connectionreferencelogicalname="new_office365users">
      <connectionreferencedisplayname>Office 365 Users Connection</connectionreferencedisplayname>
      <connectorid>/providers/Microsoft.PowerApps/apis/shared_office365users</connectorid>
      <iscustomizable>1</iscustomizable>
      <statecode>0</statecode>
    </connectionreference>
  </connectionreferences>
  
  <environmentvariables>
    <environmentvariable schemaname="new_SharePointSiteUrl">
      <displayname>SharePoint Site URL</displayname>
      <description>URL of the SharePoint site containing the PowerApp Data List</description>
      <type>100000000</type>
      <defaultvalue>https://yourtenant.sharepoint.com/sites/yoursite</defaultvalue>
      <iscustomizable>1</iscustomizable>
      <introducedversion>*******</introducedversion>
      <statecode>0</statecode>
    </environmentvariable>
    <environmentvariable schemaname="new_SharePointListName">
      <displayname>SharePoint List Name</displayname>
      <description>Name of the SharePoint list used by the PowerApp</description>
      <type>100000000</type>
      <defaultvalue>PowerApp Data List</defaultvalue>
      <iscustomizable>1</iscustomizable>
      <introducedversion>*******</introducedversion>
      <statecode>0</statecode>
    </environmentvariable>
  </environmentvariables>
  
</ImportExportXml>
