# 🚀 Deployment Guide - PowerApp SharePoint Integration

Tento návod vás prevedie kompletným procesom nasadenia PowerApp aplikácie prepojenej na SharePoint list.

## 📋 Predpoklady

### Požadované oprávnenia:
- **SharePoint**: Site Owner alebo Full Control
- **PowerApps**: Environment Maker alebo vyššie
- **Office 365**: Platná licencia s PowerApps

### Potrebný softvér:
- **PowerShell 5.1+** alebo **PowerShell Core 7+**
- **PnP PowerShell modul** (`Install-Module PnP.PowerShell`)
- **Webový prehliadač** (Chrome, Edge, Firefox)

## 🏗️ Fáza 1: Príprava SharePoint

### Automatická inštalácia (Odporúčané)

1. **Otvorte PowerShell ako administrátor**
2. **Nainštalujte PnP PowerShell modul** (ak ešte nie je nain<PERSON><PERSON><PERSON>ný):
   ```powershell
   Install-Module PnP.PowerShell -Force -AllowClobber
   ```

3. **Spustite setup script**:
   ```powershell
   .\PowerShell_Setup.ps1 -SiteUrl "https://yourtenant.sharepoint.com/sites/yoursite"
   ```

4. **Pre prepísanie existujúceho listu**:
   ```powershell
   .\PowerShell_Setup.ps1 -SiteUrl "https://yourtenant.sharepoint.com/sites/yoursite" -Force
   ```

### Manuálna inštalácia

Ak preferujete manuálne vytvorenie, postupujte podľa krokov v `README.md`.

## 🎨 Fáza 2: Vytvorenie PowerApp

### Metóda A: Vytvorenie z PowerApps portálu

1. **Prejdite na PowerApps**:
   - Otvorte [https://make.powerapps.com/](https://make.powerapps.com/)
   - Prihláste sa pomocou vášho Office 365 účtu

2. **Vytvorte novú aplikáciu**:
   ```
   Create → Canvas app from blank → Tablet layout
   ```

3. **Nastavte názov aplikácie**:
   ```
   App name: SharePoint List Manager
   Format: Tablet (16:9) alebo Phone
   ```

### Metóda B: Vytvorenie zo SharePoint

1. **Otvorte SharePoint list**:
   - Prejdite na váš SharePoint site
   - Otvorte list "PowerApp Data List"

2. **Vytvorte aplikáciu**:
   ```
   Integrate → Power Apps → Create an app
   ```

3. **Zadajte názov a vytvorte**:
   ```
   App name: SharePoint List Manager
   Create
   ```

## 🔗 Fáza 3: Konfigurácia dátového pripojenia

### Pridanie SharePoint dátového zdroja

1. **V PowerApps Studio**:
   ```
   Data (ľavý panel) → Add data → SharePoint
   ```

2. **Zadajte SharePoint URL**:
   ```
   Site URL: https://yourtenant.sharepoint.com/sites/yoursite
   ```

3. **Vyberte list**:
   ```
   ✅ PowerApp Data List
   Connect
   ```

### Pridanie Office 365 Users (pre AssignedTo pole)

1. **Pridajte ďalší dátový zdroj**:
   ```
   Data → Add data → Office 365 Users
   Connect
   ```

## 🎯 Fáza 4: Implementácia obrazoviek

### Vytvorenie obrazoviek

1. **Vytvorte tri obrazovky**:
   ```
   Tree view → Screens → New screen → Blank
   ```
   - `BrowseScreen` (premenujte Screen1)
   - `DetailsScreen`
   - `EditScreen`

2. **Nastavte štartovú obrazovku**:
   ```
   App → OnStart → Set(varCurrentScreen, "BrowseScreen")
   App → StartScreen → BrowseScreen
   ```

### Implementácia komponentov

Použite JSON súbory ako referenciu pre vytvorenie komponentov:

1. **BrowseScreen.json** - pre hlavnú obrazovku
2. **DetailsScreen.json** - pre detail položky
3. **EditScreen.json** - pre úpravu/vytvorenie

### Kopírovanie formúl

Použite súbor `PowerApp_Formulas.txt` pre:
- Globálne premenné
- Formuly pre jednotlivé komponenty
- Validačné pravidlá
- Error handling

## ⚙️ Fáza 5: Konfigurácia a testovanie

### Nastavenie globálnych premenných

1. **App.OnStart**:
   ```powerapps
   Set(gblCurrentUser, User());
   Set(gblToday, Today());
   ClearCollect(colCategories, ["Vysoká priorita", "Stredná priorita", "Nízka priorita"]);
   ClearCollect(colStatuses, ["Nový", "V riešení", "Dokončený", "Zrušený"])
   ```

### Testovanie funkcionalít

1. **Základné operácie**:
   - ✅ Zobrazenie zoznamu položiek
   - ✅ Vyhľadávanie a filtrovanie
   - ✅ Zobrazenie detailov
   - ✅ Vytvorenie novej položky
   - ✅ Úprava existujúcej položky
   - ✅ Mazanie položky

2. **Validácie**:
   - ✅ Povinné polia
   - ✅ Dátové typy
   - ✅ Oprávnenia

## 📱 Fáza 6: Publikovanie a zdieľanie

### Uloženie a publikovanie

1. **Uložte aplikáciu**:
   ```
   File → Save → Save as → SharePoint List Manager
   ```

2. **Publikujte aplikáciu**:
   ```
   File → Publish → Publish this version
   ```

### Zdieľanie s používateľmi

1. **Nastavte oprávnenia**:
   ```
   File → Share → Add people
   ```

2. **Typy oprávnení**:
   - **Can view**: Iba zobrazenie
   - **Can edit**: Úprava aplikácie
   - **Can use**: Používanie aplikácie

### Pridanie do Teams (voliteľné)

1. **Pridajte ako Teams app**:
   ```
   File → Add to Teams → Add to a team/channel
   ```

## 🔧 Fáza 7: Údržba a monitoring

### Pravidelné úlohy

1. **Backup aplikácie**:
   ```
   File → Export package → Download
   ```

2. **Monitoring výkonu**:
   ```
   PowerApps Admin Center → Analytics → Usage reports
   ```

3. **Aktualizácie**:
   - Pravidelne kontrolujte dostupné aktualizácie
   - Testujte nové funkcionality v dev prostredí

### Riešenie problémov

1. **Loggy a diagnostika**:
   ```
   Monitor → View logs
   ```

2. **Časté problémy**:
   - Pomalé načítavanie → Optimalizujte formuly
   - Chyby pripojenia → Skontrolujte oprávnenia
   - Nesprávne zobrazenie → Skontrolujte responsive design

## 📊 Fáza 8: Optimalizácia výkonu

### Best practices

1. **Delegable formulas**:
   ```powerapps
   // Dobré - delegable
   Filter('PowerApp Data List', Status = "Aktívny")
   
   // Zlé - non-delegable
   Filter('PowerApp Data List', Upper(Title) = "TEST")
   ```

2. **Lazy loading**:
   ```powerapps
   // Načítaj dáta len keď sú potrebné
   If(IsBlank(colData), ClearCollect(colData, 'PowerApp Data List'))
   ```

3. **Caching**:
   ```powerapps
   // Použite kolekcie pre často používané dáta
   ClearCollect(colCategories, Distinct('PowerApp Data List', Category))
   ```

## 🔒 Fáza 9: Bezpečnosť

### Nastavenie oprávnení

1. **SharePoint list permissions**:
   - Read: Všetci používatelia
   - Write: Iba autorizovaní používatelia
   - Delete: Iba administrátori

2. **PowerApp permissions**:
   - Rozdeľte používateľov do skupín
   - Nastavte role-based access

### Data Loss Prevention (DLP)

1. **Nastavte DLP policies**:
   ```
   PowerApps Admin Center → Data policies → New policy
   ```

2. **Klasifikujte connectors**:
   - Business: SharePoint, Office 365 Users
   - Non-business: Ostatné external connectors

## ✅ Checklist pre nasadenie

### Pre-deployment
- [ ] SharePoint list vytvorený a nakonfigurovaný
- [ ] Oprávnenia nastavené
- [ ] Testové dáta pridané
- [ ] PowerApp vytvorená a otestovaná
- [ ] Všetky obrazovky implementované
- [ ] Formuly otestované

### Post-deployment
- [ ] Aplikácia publikovaná
- [ ] Používatelia informovaní
- [ ] Dokumentácia poskytnutá
- [ ] Monitoring nastavený
- [ ] Backup vytvorený
- [ ] Support proces definovaný

## 📞 Podpora

### Kontakty
- **IT Helpdesk**: [<EMAIL>]
- **PowerApps Admin**: [<EMAIL>]
- **SharePoint Admin**: [<EMAIL>]

### Užitočné odkazy
- [PowerApps Documentation](https://docs.microsoft.com/en-us/powerapps/)
- [SharePoint Documentation](https://docs.microsoft.com/en-us/sharepoint/)
- [PowerApps Community](https://powerusers.microsoft.com/t5/PowerApps-Community/ct-p/PowerApps1)

---

**Verzia:** 1.0  
**Dátum:** Január 2024  
**Autor:** Your Name
