# PowerApp Formulas Reference
# Zoznam všetkých dôležitých formúl pre PowerApp aplikáciu

## GLOBÁLNE PREMENNÉ (App.OnStart)

// Inicializácia globálnych premenných
Set(gblCurrentUser, User());
Set(gblToday, Today());
Set(gblAppVersion, "1.0.0");

// Definícia farieb témy
Set(gblThemeColors, {
    Primary: RGBA(0, 120, 212, 1),
    Secondary: RGBA(108, 117, 125, 1),
    Success: RGBA(40, 167, 69, 1),
    Warning: RGBA(255, 193, 7, 1),
    Danger: RGBA(220, 53, 69, 1),
    Light: RGBA(248, 249, 250, 1),
    Dark: RGBA(33, 37, 41, 1)
});

// Kolekcie pre dropdown hodnoty
ClearCollect(colCategories, ["Vysoká priorita", "Stred<PERSON> priorita", "Nízka priorita"]);
ClearCollect(colStatuses, ["<PERSON><PERSON>", "<PERSON> riešení", "<PERSON><PERSON><PERSON>ený", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]);

## BROWSE SCREEN FORMULAS

### Gallery Items (ItemsGallery.Items)
Filter(
    'PowerApp Data List',
    (IsBlank(varSearchText) || varSearchText in Title || varSearchText in Description) &&
    (IsBlank(varCategoryFilter) || Category = varCategoryFilter) &&
    (IsBlank(varStatusFilter) || Status = varStatusFilter)
)

### Search Input (SearchInput.OnChange)
Set(varSearchText, SearchInput.Text)

### Category Filter (CategoryFilter.OnChange)
Set(varCategoryFilter, CategoryFilter.Selected.Value)

### Status Filter (StatusFilter.OnChange)
Set(varStatusFilter, StatusFilter.Selected.Value)

### Clear Filters Button (ClearFiltersButton.OnSelect)
Reset(CategoryFilter);
Reset(StatusFilter);
Set(varCategoryFilter, Blank());
Set(varStatusFilter, Blank());
Set(varSearchText, "");
Reset(SearchInput)

### Refresh Button (RefreshButton.OnSelect)
Refresh('PowerApp Data List');
Notify("Dáta boli obnovené", NotificationType.Success)

### Add Button (AddButton.OnSelect)
Set(varSelectedItem, Blank());
Navigate(EditScreen, ScreenTransition.Fade)

### Gallery Item Selection (ItemsGallery.OnSelect)
Set(varSelectedItem, ThisItem);
Navigate(DetailsScreen, ScreenTransition.Fade)

### Category Badge Color (CategoryLabel.Fill)
Switch(
    ThisItem.Category,
    "Vysoká priorita", RGBA(220, 53, 69, 1),
    "Stredná priorita", RGBA(255, 193, 7, 1),
    "Nízka priorita", RGBA(40, 167, 69, 1),
    RGBA(108, 117, 125, 1)
)

### Status Label Color (StatusLabel.Color)
Switch(
    ThisItem.Status,
    "Dokončený", RGBA(40, 167, 69, 1),
    "V riešení", RGBA(0, 120, 212, 1),
    "Nový", RGBA(255, 193, 7, 1),
    "Zrušený", RGBA(220, 53, 69, 1),
    RGBA(108, 117, 125, 1)
)

### Due Date Color (DueDateLabel.Color)
If(
    ThisItem.DueDate < Today() && ThisItem.Status <> "Dokončený",
    RGBA(220, 53, 69, 1),
    RGBA(108, 117, 125, 1)
)

### Progress Bar Width (ProgressBar.Width)
100 * (ThisItem.Progress / 100)

## DETAILS SCREEN FORMULAS

### Screen Validation (DetailsScreen.OnVisible)
If(IsBlank(varSelectedItem), Navigate(BrowseScreen, ScreenTransition.Fade))

### Back Button (BackButton.OnSelect)
Navigate(BrowseScreen, ScreenTransition.Fade)

### Edit Button (EditButton.OnSelect)
Navigate(EditScreen, ScreenTransition.Fade)

### Title Display (TitleLabel.Text)
varSelectedItem.Title

### Created Info (CreatedInfo.Text)
"Vytvorené: " & Text(varSelectedItem.Created, "dd.mm.yyyy hh:mm") & " | Autor: " & varSelectedItem.Author.DisplayName

### Status Badge (StatusBadge.Fill)
Switch(
    varSelectedItem.Status,
    "Dokončený", RGBA(40, 167, 69, 1),
    "V riešení", RGBA(0, 120, 212, 1),
    "Nový", RGBA(255, 193, 7, 1),
    "Zrušený", RGBA(220, 53, 69, 1),
    RGBA(108, 117, 125, 1)
)

### Description Display (DescriptionText.Text)
If(IsBlank(varSelectedItem.Description), "Bez popisu", varSelectedItem.Description)

### Due Date Display (DueDateValue.Text)
If(IsBlank(varSelectedItem.DueDate), "Nie je stanovený", Text(varSelectedItem.DueDate, "dd.mm.yyyy"))

### Assigned To Display (AssignedToValue.Text)
If(IsBlank(varSelectedItem.AssignedTo), "Nie je priradené", varSelectedItem.AssignedTo.DisplayName)

### Tags Display (TagsValue.Text)
If(IsBlank(varSelectedItem.Tags), "Žiadne štítky", varSelectedItem.Tags)

## EDIT SCREEN FORMULAS

### Screen Title (ScreenTitle.Text)
If(IsBlank(varSelectedItem), "Nová položka", "Úprava položky")

### Cancel Button (BackButton.OnSelect)
If(
    IsBlank(varSelectedItem),
    Navigate(BrowseScreen, ScreenTransition.Fade),
    Navigate(DetailsScreen, ScreenTransition.Fade)
)

### Title Input Default (TitleInput.Default)
If(IsBlank(varSelectedItem), "", varSelectedItem.Title)

### Description Input Default (DescriptionInput.Default)
If(IsBlank(varSelectedItem), "", varSelectedItem.Description)

### Category Dropdown Default (CategoryDropdown.DefaultSelectedItems)
If(IsBlank(varSelectedItem), ["Stredná priorita"], [varSelectedItem.Category])

### Status Dropdown Default (StatusDropdown.DefaultSelectedItems)
If(IsBlank(varSelectedItem), ["Nový"], [varSelectedItem.Status])

### Progress Slider Default (ProgressSlider.Default)
If(IsBlank(varSelectedItem), 0, varSelectedItem.Progress)

### Progress Value Display (ProgressValue.Text)
ProgressSlider.Value & "%"

### Due Date Picker Default (DueDatePicker.DefaultDate)
If(IsBlank(varSelectedItem) || IsBlank(varSelectedItem.DueDate), Blank(), varSelectedItem.DueDate)

### Assigned To Combo Default (AssignedToCombo.DefaultSelectedItems)
If(IsBlank(varSelectedItem) || IsBlank(varSelectedItem.AssignedTo), [], [varSelectedItem.AssignedTo])

### Tags Input Default (TagsInput.Default)
If(IsBlank(varSelectedItem), "", varSelectedItem.Tags)

### Save Button (SaveButton.OnSelect)
If(
    IsBlank(varSelectedItem),
    // Vytvorenie novej položky
    Patch(
        'PowerApp Data List',
        Defaults('PowerApp Data List'),
        {
            Title: TitleInput.Text,
            Description: DescriptionInput.Text,
            Category: CategoryDropdown.Selected.Value,
            Status: StatusDropdown.Selected.Value,
            DueDate: If(IsBlank(DueDatePicker.SelectedDate), Blank(), DueDatePicker.SelectedDate),
            AssignedTo: If(IsBlank(AssignedToCombo.Selected), Blank(), AssignedToCombo.Selected),
            Progress: Value(ProgressSlider.Value),
            Tags: TagsInput.Text
        }
    );
    Notify("Položka bola vytvorená", NotificationType.Success);
    Navigate(BrowseScreen, ScreenTransition.Fade),
    // Úprava existujúcej položky
    Patch(
        'PowerApp Data List',
        varSelectedItem,
        {
            Title: TitleInput.Text,
            Description: DescriptionInput.Text,
            Category: CategoryDropdown.Selected.Value,
            Status: StatusDropdown.Selected.Value,
            DueDate: If(IsBlank(DueDatePicker.SelectedDate), Blank(), DueDatePicker.SelectedDate),
            AssignedTo: If(IsBlank(AssignedToCombo.Selected), Blank(), AssignedToCombo.Selected),
            Progress: Value(ProgressSlider.Value),
            Tags: TagsInput.Text
        }
    );
    Notify("Položka bola aktualizovaná", NotificationType.Success);
    Navigate(DetailsScreen, ScreenTransition.Fade)
)

### Delete Button Visibility (DeleteButton.Visible)
!IsBlank(varSelectedItem)

### Delete Button (DeleteButton.OnSelect)
If(
    ConfirmDialog.Confirm("Naozaj chcete zmazať túto položku?", "Zmazanie položky", "Zmazať", "Zrušiť"),
    Remove('PowerApp Data List', varSelectedItem);
    Notify("Položka bola zmazaná", NotificationType.Success);
    Navigate(BrowseScreen, ScreenTransition.Fade)
)

## VALIDAČNÉ FORMULY

### Required Field Validation (TitleInput)
If(IsBlank(TitleInput.Text), "Názov je povinný", "")

### Email Validation (pre budúce použitie)
If(IsMatch(EmailInput.Text, Email), "", "Neplatný email formát")

### Date Validation (DueDatePicker)
If(DueDatePicker.SelectedDate < Today(), "Termín nemôže byť v minulosti", "")

## POMOCNÉ FORMULY

### Format Date
Text(DateValue, "dd.mm.yyyy")

### Format DateTime
Text(DateTimeValue, "dd.mm.yyyy hh:mm")

### Truncate Text
Left(LongText, 100) & If(Len(LongText) > 100, "...", "")

### Calculate Days Remaining
DateDiff(Today(), DueDateValue, Days)

### Progress Color Based on Value
If(
    ProgressValue >= 75, RGBA(40, 167, 69, 1),    // Green
    ProgressValue >= 50, RGBA(255, 193, 7, 1),    // Yellow
    ProgressValue >= 25, RGBA(255, 152, 0, 1),    // Orange
    RGBA(220, 53, 69, 1)                          // Red
)

### User Display Name
If(IsBlank(UserField), "Nepriradené", UserField.DisplayName)

### Collection Filtering
Filter(Collection, SearchText in FieldName)

### Collection Sorting
Sort(Collection, FieldName, SortOrder.Ascending)

## ERROR HANDLING

### Connection Error
If(
    IsError(DataSource),
    Notify("Chyba pripojenia k dátam", NotificationType.Error),
    // Normal operation
)

### Save Error Handling
IfError(
    Patch('PowerApp Data List', ...),
    Notify("Chyba pri ukladaní: " & FirstError.Message, NotificationType.Error),
    Notify("Úspešne uložené", NotificationType.Success)
)
