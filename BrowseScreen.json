{"screenName": "BrowseScreen", "displayName": "Prehľad položiek", "description": "Hlavná obrazovka zobrazujúca zoznam všetkých položiek zo SharePoint listu", "layout": {"type": "Vertical", "padding": 16, "spacing": 12}, "controls": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "Container", "properties": {"Height": 80, "Fill": "RGBA(0, 120, 212, 1)", "BorderRadius": 0}, "children": [{"name": "AppTitle", "type": "Label", "properties": {"Text": "SharePoint List Manager", "X": 20, "Y": 20, "Width": 300, "Height": 40, "Size": 18, "FontWeight": "Bold", "Color": "RGBA(255, 255, 255, 1)", "Align": "Left"}}, {"name": "RefreshButton", "type": "<PERSON><PERSON>", "properties": {"Text": "Obnoviť", "X": "<PERSON><PERSON><PERSON>idth - 120", "Y": 25, "Width": 100, "Height": 30, "Fill": "RGBA(255, 255, 255, 0.2)", "Color": "RGBA(255, 255, 255, 1)", "BorderColor": "RGBA(255, 255, 255, 1)", "OnSelect": "Refresh('PowerApp Data List'); Notify(\"Dáta boli obnovené\", NotificationType.Success)"}}]}, {"name": "SearchContainer", "type": "Container", "properties": {"Height": 60, "Fill": "RGBA(248, 249, 250, 1)", "BorderColor": "RGBA(222, 226, 230, 1)", "BorderThickness": 1}, "children": [{"name": "SearchInput", "type": "TextInput", "properties": {"HintText": "Hľadať v názve alebo popise...", "X": 20, "Y": 15, "Width": "<PERSON><PERSON><PERSON>th - 160", "Height": 30, "BorderColor": "RGBA(206, 212, 218, 1)", "Default": "", "OnChange": "Set(varSearchText, SearchInput.Text)"}}, {"name": "AddButton", "type": "<PERSON><PERSON>", "properties": {"Text": "+ Pridať", "X": "<PERSON><PERSON><PERSON>idth - 120", "Y": 15, "Width": 100, "Height": 30, "Fill": "RGBA(40, 167, 69, 1)", "Color": "RGBA(255, 255, 255, 1)", "OnSelect": "Set(varSelectedItem, Blank()); Navigate(EditScreen, ScreenTransition.Fade)"}}]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "Container", "properties": {"Height": 50, "Fill": "RGBA(255, 255, 255, 1)"}, "children": [{"name": "CategoryFilter", "type": "Dropdown", "properties": {"Items": "Distinct('PowerApp Data List', Category)", "DefaultSelectedItems": "[]", "HintText": "Filtrovať podľa kategórie", "X": 20, "Y": 10, "Width": 200, "Height": 30, "OnChange": "Set(var<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CategoryFilter.Selected.Value)"}}, {"name": "StatusFilter", "type": "Dropdown", "properties": {"Items": "Distinct('PowerApp Data List', Status)", "DefaultSelectedItems": "[]", "HintText": "Filtrovať podľa stavu", "X": 240, "Y": 10, "Width": 200, "Height": 30, "OnChange": "Set(var<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, StatusFilter.Selected.Value)"}}, {"name": "ClearFiltersButton", "type": "<PERSON><PERSON>", "properties": {"Text": "Vymazať filtre", "X": 460, "Y": 10, "Width": 120, "Height": 30, "Fill": "RGBA(108, 117, 125, 1)", "Color": "RGBA(255, 255, 255, 1)", "OnSelect": "Reset(CategoryFilter); Reset(StatusFilter); Set(varCategoryFilter, Blank()); Set(varStatusFilter, Blank()); Set(varSearchText, \"\"); Reset(SearchInput)"}}]}, {"name": "ItemsGallery", "type": "Gallery", "properties": {"Items": "Filter(\n  'PowerApp Data List',\n  (IsBlank(varSearchText) || varSearchText in Title || varSearchText in Description) &&\n  (IsBlank(varCategoryFilter) || Category = varCategoryFilter) &&\n  (IsBlank(varStatusFilter) || Status = varStatusFilter)\n)", "Layout": "Vertical", "TemplateSize": 120, "X": 0, "Y": 190, "Width": "<PERSON><PERSON><PERSON>", "Height": "Parent.Height - 190", "BorderColor": "RGBA(222, 226, 230, 1)", "BorderThickness": 1, "OnSelect": "Set(varSelectedItem, ThisItem); Navigate(DetailsScreen, ScreenTransition.Fade)"}, "template": {"name": "ItemTemplate", "controls": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "Container", "properties": {"Height": 110, "Fill": "If(ThisItem.Status = \"Dokončený\", RGBA(212, 237, 218, 1), If(ThisItem.Category = \"Vysoká priorita\", RGBA(248, 215, 218, 1), RGBA(255, 255, 255, 1)))", "BorderColor": "RGBA(222, 226, 230, 1)", "BorderThickness": 1, "BorderRadius": 4}, "children": [{"name": "TitleLabel", "type": "Label", "properties": {"Text": "ThisItem.Title", "X": 15, "Y": 10, "Width": "<PERSON><PERSON><PERSON>idth - 150", "Height": 25, "Size": 14, "FontWeight": "Bold", "Color": "RGBA(33, 37, 41, 1)"}}, {"name": "DescriptionLabel", "type": "Label", "properties": {"Text": "Left(ThisItem.Description, 100) & If(Len(ThisItem.Description) > 100, \"...\", \"\")", "X": 15, "Y": 35, "Width": "<PERSON><PERSON><PERSON>idth - 150", "Height": 40, "Size": 11, "Color": "RGBA(108, 117, 125, 1)"}}, {"name": "CategoryLabel", "type": "Label", "properties": {"Text": "ThisItem.Category", "X": 15, "Y": 80, "Width": 120, "Height": 20, "Size": 10, "Color": "RGBA(255, 255, 255, 1)", "Fill": "Switch(ThisItem.Category, \"Vysoká priorita\", RGBA(220, 53, 69, 1), \"Stredná priorita\", RGBA(255, 193, 7, 1), \"Nízka priorita\", RGBA(40, 167, 69, 1), RGBA(108, 117, 125, 1))", "Align": "Center", "BorderRadius": 12}}, {"name": "StatusLabel", "type": "Label", "properties": {"Text": "ThisItem.Status", "X": "<PERSON><PERSON><PERSON>idth - 120", "Y": 10, "Width": 100, "Height": 25, "Size": 11, "FontWeight": "SemiBold", "Color": "Switch(ThisItem.Status, \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", RGBA(40, 167, 69, 1), \"V riešení\", RGBA(0, 120, 212, 1), \"Nový\", RGBA(255, 193, 7, 1), \"<PERSON><PERSON><PERSON><PERSON>ý\", RGBA(220, 53, 69, 1), RGBA(108, 117, 125, 1))", "Align": "Center"}}, {"name": "DueDateLabel", "type": "Label", "properties": {"Text": "If(IsBlank(ThisItem.DueDate), \"<PERSON>z termínu\", Text(ThisItem.DueDate, \"dd.mm.yyyy\"))", "X": "<PERSON><PERSON><PERSON>idth - 120", "Y": 40, "Width": 100, "Height": 20, "Size": 10, "Color": "If(ThisItem.DueDate < Today() && ThisItem.Status <> \"Dokončený\", RGBA(220, 53, 69, 1), RGBA(108, 117, 125, 1))", "Align": "Center"}}, {"name": "ProgressBar", "type": "Rectangle", "properties": {"X": "<PERSON><PERSON><PERSON>idth - 120", "Y": 70, "Width": "100 * (ThisItem.Progress / 100)", "Height": 4, "Fill": "RGBA(0, 120, 212, 1)", "BorderThickness": 0}}, {"name": "ProgressBackground", "type": "Rectangle", "properties": {"X": "<PERSON><PERSON><PERSON>idth - 120", "Y": 70, "Width": 100, "Height": 4, "Fill": "RGBA(222, 226, 230, 1)", "BorderThickness": 0}}]}]}}], "onVisible": "Set(varSearchText, \"\"); Set(var<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Blank()); Set(var<PERSON><PERSON>us<PERSON><PERSON><PERSON>, Blank())", "variables": [{"name": "varSearchText", "type": "Text", "defaultValue": "", "description": "Text pre vyhľadávanie"}, {"name": "varCategoryFilter", "type": "Text", "defaultValue": "Blank()", "description": "Filter pre kategóriu"}, {"name": "varStatusFilter", "type": "Text", "defaultValue": "Blank()", "description": "Filter pre stav"}]}