<?xml version="1.0" encoding="utf-8"?>
<DataSourceInfo xmlns="http://schemas.microsoft.com/powerapps/2016/11/cdsbasedatasourceinfoprovider">
  <DataSource Name="PowerAppDataList" Kind="SharePointList">
    <DisplayName>PowerApp Data List</DisplayName>
    <ApiId>/providers/Microsoft.PowerApps/apis/shared_sharepointonline</ApiId>
    <Type>SharePointListEntity</Type>
    <CreatedTime>2024-01-15T10:00:00.000Z</CreatedTime>
    <ModifiedTime>2024-01-15T10:00:00.000Z</ModifiedTime>
    <IsOnPremises>false</IsOnPremises>
    <IsSampleData>false</IsSampleData>
    <IsWritable>true</IsWritable>
    <IsRefreshable>true</IsRefreshable>
    <DefaultConnectionId>shared-sharepointonline-47ea4a8c</DefaultConnectionId>
    <DatasetName>https://{tenant}.sharepoint.com/sites/{site}</DatasetName>
    <EntityName>PowerAppDataList</EntityName>
    <EntitySetName>PowerAppDataList</EntitySetName>
    <LogicalName>PowerApp Data List</LogicalName>
    <CollectionName>PowerAppDataList</CollectionName>
    <EntityContainer>PowerAppDataListContainer</EntityContainer>
    <MinVersionToLoad>1.331</MinVersionToLoad>
    <TableDefinition>
      <Name>PowerAppDataList</Name>
      <DisplayName>PowerApp Data List</DisplayName>
      <Kind>SharePointList</Kind>
      <Columns>
        <Column Name="ID" DisplayName="ID" IsPrimaryKey="true" IsRequired="false" IsReadOnly="true" Type="Number" />
        <Column Name="Title" DisplayName="Názov" IsRequired="true" Type="Text" MaxLength="255" />
        <Column Name="Description" DisplayName="Popis" IsRequired="false" Type="Memo" />
        <Column Name="Category" DisplayName="Kategória" IsRequired="true" Type="Choice" DefaultValue="Stredná priorita">
          <Choices>
            <Choice>Vysoká priorita</Choice>
            <Choice>Stredná priorita</Choice>
            <Choice>Nízka priorita</Choice>
          </Choices>
        </Column>
        <Column Name="Status" DisplayName="Stav" IsRequired="true" Type="Choice" DefaultValue="Nový">
          <Choices>
            <Choice>Nový</Choice>
            <Choice>V riešení</Choice>
            <Choice>Dokončený</Choice>
            <Choice>Zrušený</Choice>
          </Choices>
        </Column>
        <Column Name="DueDate" DisplayName="Termín dokončenia" IsRequired="false" Type="DateTime" Format="DateOnly" />
        <Column Name="AssignedTo" DisplayName="Priradené komu" IsRequired="false" Type="Lookup" LookupTable="UserInfo" />
        <Column Name="Progress" DisplayName="Pokrok (%)" IsRequired="false" Type="Number" Min="0" Max="100" DefaultValue="0" />
        <Column Name="Tags" DisplayName="Štítky" IsRequired="false" Type="Text" MaxLength="500" />
        <Column Name="Created" DisplayName="Vytvorené" IsRequired="false" IsReadOnly="true" Type="DateTime" />
        <Column Name="Modified" DisplayName="Upravené" IsRequired="false" IsReadOnly="true" Type="DateTime" />
        <Column Name="Author" DisplayName="Autor" IsRequired="false" IsReadOnly="true" Type="Lookup" LookupTable="UserInfo" />
        <Column Name="Editor" DisplayName="Editor" IsRequired="false" IsReadOnly="true" Type="Lookup" LookupTable="UserInfo" />
      </Columns>
    </TableDefinition>
    <WadlMetadata>
      <![CDATA[
      <application xmlns="http://wadl.dev.java.net/2009/02">
        <resources base="https://{tenant}.sharepoint.com/sites/{site}/_api/web/lists/getbytitle('PowerApp%20Data%20List')">
          <resource path="items">
            <method name="GET">
              <response>
                <representation mediaType="application/json"/>
              </response>
            </method>
            <method name="POST">
              <request>
                <representation mediaType="application/json"/>
              </request>
              <response>
                <representation mediaType="application/json"/>
              </response>
            </method>
          </resource>
          <resource path="items({id})">
            <param name="id" style="template" type="xs:int"/>
            <method name="GET">
              <response>
                <representation mediaType="application/json"/>
              </response>
            </method>
            <method name="PATCH">
              <request>
                <representation mediaType="application/json"/>
              </request>
              <response>
                <representation mediaType="application/json"/>
              </response>
            </method>
            <method name="DELETE">
              <response status="204"/>
            </method>
          </resource>
        </resources>
      </application>
      ]]>
    </WadlMetadata>
  </DataSource>
  <DataSource Name="Office365Users" Kind="Office365Users">
    <DisplayName>Office 365 Users</DisplayName>
    <ApiId>/providers/Microsoft.PowerApps/apis/shared_office365users</ApiId>
    <Type>Office365UsersEntity</Type>
    <CreatedTime>2024-01-15T10:00:00.000Z</CreatedTime>
    <ModifiedTime>2024-01-15T10:00:00.000Z</ModifiedTime>
    <IsOnPremises>false</IsOnPremises>
    <IsSampleData>false</IsSampleData>
    <IsWritable>false</IsWritable>
    <IsRefreshable>true</IsRefreshable>
    <DefaultConnectionId>shared-office365users-47ea4a8c</DefaultConnectionId>
    <DatasetName>default</DatasetName>
    <EntityName>Users</EntityName>
    <EntitySetName>Users</EntitySetName>
    <LogicalName>Users</LogicalName>
    <CollectionName>Users</CollectionName>
    <EntityContainer>Office365UsersContainer</EntityContainer>
    <MinVersionToLoad>1.331</MinVersionToLoad>
  </DataSource>
</DataSourceInfo>
