{"ComponentIndexes": {}, "ControlUniqueGuids": {"App": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f3a", "BrowseScreen": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f3b", "DetailsScreen": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f3c", "EditScreen": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f3d", "HeaderContainer": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f3e", "AppTitle": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f3f", "RefreshButton": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f40", "SearchContainer": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f41", "SearchInput": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f42", "AddButton": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f43", "FilterContainer": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f44", "CategoryFilter": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f45", "StatusFilter": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f46", "ClearFiltersButton": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f47", "ItemsGallery": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f48", "TitleLabel": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f49", "DescriptionLabel": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f4a", "CategoryLabel": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f4b", "StatusLabel": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f4c", "DueDateLabel": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f4d", "ProgressBarBackground": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f4e", "ProgressBar": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f4f", "BackButton": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f50", "ScreenTitle": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f51", "EditButton": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f52", "ContentContainer": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f53", "TitleSection": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f54", "CreatedInfo": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f55", "StatusSection": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f56", "StatusContainer": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f57", "StatusLabelTitle": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f58", "StatusBadge": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f59", "CategoryLabelTitle": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f5a", "CategoryBadge": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f5b", "ProgressContainer": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f5c", "ProgressHeader": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f5d", "ProgressLabelTitle": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f5e", "ProgressValue": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f5f", "ProgressBarFill": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f60", "DetailsSection": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f61", "DescriptionTitle": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f62", "DescriptionText": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f63", "DueDateTitle": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f64", "DueDateValue": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f65", "AssignedToTitle": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f66", "AssignedToValue": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f67", "TagsSection": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f68", "TagsTitle": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f69", "TagsValue": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f6a", "SaveButton": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f6b", "DeleteButton": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f6c", "FormContainer": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f6d", "BasicInfoSection": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f6e", "TitleInput": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f6f", "DescriptionInput": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f70", "CategoryRow": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f71", "CategoryDropdown": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f72", "StatusDropdown": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f73", "ProgressRow": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f74", "ProgressSlider": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f75", "DateAssignedRow": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f76", "DueDatePicker": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f77", "AssignedToCombo": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f78", "TagsRow": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f79", "TagsInput": "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f7a"}, "FunctionParamsInvariantScripts": {}, "FunctionParamsInvariantScriptsOnInstances": {}, "HeaderLastSavedDateTimeUTC": "2024-01-15T10:00:00.000Z", "IsLegacyComponentAllowGlobalScopeCase": false, "LocalConnectionIDReferences": {"47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f3a": "shared-sharepointonline-47ea4a8c", "47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f3b": "shared-office365users-47ea4a8c"}, "LocalDatabaseReferencesAsEmpty": false, "LocalResourceFileNames": {"logo": "logo.jpg"}, "LocalResourceRootPaths": {"logo": "https://pacontainerimages.blob.core.windows.net/images/47ea4a8c-2ac5-4c8d-9b4f-8a7c6d5e4f3a"}, "OrderComponentMetadata": {}, "OrderComponentTemplate": {}, "OrderDataSource": {"PowerAppDataList": 0, "Office365Users": 1}, "OrderGroupControls": {}, "OrderPcfTemplate": {}, "OrderTemplate": {}, "OrderXMLTemplate": {"button": 3, "combobox": 8, "datePicker": 7, "dropdown": 4, "gallery": 2, "groupContainer": 0, "label": 1, "rectangle": 6, "slider": 9, "text": 5}, "OverridablePropertiesEntry": {"App": {}, "BrowseScreen": {}, "DetailsScreen": {}, "EditScreen": {}}, "PCFDynamicSchemaForIRRetrievalEntry": {}, "PublishOrderIndices": {"App": 0, "BrowseScreen": 0, "DetailsScreen": 0, "EditScreen": 0}, "ResourcesJsonIndices": {"Image-logo": 0}, "RuleScreenIdWithoutScreen": {}, "TemplateVersions": {}, "VolatileProperties": {"AnalysisLoadTime": 0.0, "ControlCount": {"button": 6, "combobox": 1, "datePicker": 1, "dropdown": 3, "gallery": 1, "groupContainer": 15, "label": 25, "rectangle": 3, "screen": 3, "slider": 1, "text": 4}, "DeserializationLoadTime": 0.0}, "WasLocalDatabaseReferencesEmpty": true}