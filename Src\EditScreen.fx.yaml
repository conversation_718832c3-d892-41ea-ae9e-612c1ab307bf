EditScreen As screen:
    Fill: =RGBA(248, 249, 250, 1)

    HeaderContainer As groupContainer.verticalAutoLayoutContainer:
        Fill: =RGBA(0, 120, 212, 1)
        Height: =80
        LayoutDirection: =LayoutDirection.Vertical
        LayoutGap: =0
        LayoutMode: =LayoutMode.Auto
        RadiusBottomLeft: =0
        RadiusBottomRight: =0
        RadiusTopLeft: =0
        RadiusTopRight: =0
        Width: =Parent.Width
        X: =0
        Y: =0
        ZIndex: =1

        BackButton As button:
            BorderColor: =RGBA(255, 255, 255, 1)
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(255, 255, 255, 0.2)
            Height: =30
            OnSelect: =If(IsBlank(varSelectedItem), Navigate(BrowseScreen, ScreenTransition.Fade), Navigate(DetailsScreen, ScreenTransition.Fade))
            Text: ="← Zrušiť"
            Width: =80
            X: =20
            Y: =25
            ZIndex: =1

        ScreenTitle As label:
            Align: =Align.Left
            Color: =RGBA(255, 255, 255, 1)
            FontWeight: =FontWeight.Bold
            Height: =40
            Size: =18
            Text: =If(IsBlank(varSelectedItem), "Nová položka", "Úprava položky")
            Width: =200
            X: =120
            Y: =20
            ZIndex: =2

        SaveButton As button:
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(40, 167, 69, 1)
            Height: =30
            OnSelect: |-
                =If(
                    IsBlank(varSelectedItem),
                    // Vytvorenie novej položky
                    Patch(
                        PowerAppDataList,
                        Defaults(PowerAppDataList),
                        {
                            Title: TitleInput.Text,
                            Description: DescriptionInput.Text,
                            Category: CategoryDropdown.Selected.Value,
                            Status: StatusDropdown.Selected.Value,
                            DueDate: If(IsBlank(DueDatePicker.SelectedDate), Blank(), DueDatePicker.SelectedDate),
                            AssignedTo: If(IsBlank(AssignedToCombo.Selected), Blank(), AssignedToCombo.Selected),
                            Progress: Value(ProgressSlider.Value),
                            Tags: TagsInput.Text
                        }
                    );
                    Notify("Položka bola vytvorená", NotificationType.Success);
                    Navigate(BrowseScreen, ScreenTransition.Fade),
                    // Úprava existujúcej položky
                    Patch(
                        PowerAppDataList,
                        varSelectedItem,
                        {
                            Title: TitleInput.Text,
                            Description: DescriptionInput.Text,
                            Category: CategoryDropdown.Selected.Value,
                            Status: StatusDropdown.Selected.Value,
                            DueDate: If(IsBlank(DueDatePicker.SelectedDate), Blank(), DueDatePicker.SelectedDate),
                            AssignedTo: If(IsBlank(AssignedToCombo.Selected), Blank(), AssignedToCombo.Selected),
                            Progress: Value(ProgressSlider.Value),
                            Tags: TagsInput.Text
                        }
                    );
                    Notify("Položka bola aktualizovaná", NotificationType.Success);
                    Navigate(DetailsScreen, ScreenTransition.Fade)
                )
            Text: ="Uložiť"
            Width: =70
            X: =Parent.Width - 180
            Y: =25
            ZIndex: =3

        DeleteButton As button:
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(220, 53, 69, 1)
            Height: =30
            OnSelect: |-
                =If(
                    Confirm("Naozaj chcete zmazať túto položku?"),
                    Remove(PowerAppDataList, varSelectedItem);
                    Notify("Položka bola zmazaná", NotificationType.Success);
                    Navigate(BrowseScreen, ScreenTransition.Fade)
                )
            Text: ="Zmazať"
            Visible: =!IsBlank(varSelectedItem)
            Width: =80
            X: =Parent.Width - 100
            Y: =25
            ZIndex: =4

    FormContainer As groupContainer.verticalAutoLayoutContainer:
        Fill: =RGBA(248, 249, 250, 1)
        Height: =Parent.Height - 90
        LayoutDirection: =LayoutDirection.Vertical
        LayoutGap: =20
        LayoutMode: =LayoutMode.Auto
        PaddingBottom: =20
        PaddingLeft: =20
        PaddingRight: =20
        PaddingTop: =20
        Width: =Parent.Width
        X: =0
        Y: =90
        ZIndex: =2

        BasicInfoSection As groupContainer.verticalAutoLayoutContainer:
            BorderColor: =RGBA(222, 226, 230, 1)
            BorderRadius: =8
            BorderThickness: =1
            Fill: =RGBA(255, 255, 255, 1)
            Height: =180
            LayoutDirection: =LayoutDirection.Vertical
            LayoutGap: =10
            LayoutMode: =LayoutMode.Auto
            PaddingBottom: =16
            PaddingLeft: =16
            PaddingRight: =16
            PaddingTop: =16
            Width: =Parent.Width
            ZIndex: =1

            TitleLabel As label:
                Color: =RGBA(33, 37, 41, 1)
                FontWeight: =FontWeight.Semibold
                Height: =25
                Size: =12
                Text: ="Názov *"
                Width: =100
                X: =0
                Y: =0
                ZIndex: =1

            TitleInput As text:
                BorderColor: =RGBA(206, 212, 218, 1)
                Default: =If(IsBlank(varSelectedItem), "", varSelectedItem.Title)
                Height: =35
                HintText: ="Zadajte názov položky"
                Size: =12
                Width: =Parent.Width - 20
                X: =0
                Y: =30
                ZIndex: =2

            DescriptionLabel As label:
                Color: =RGBA(33, 37, 41, 1)
                FontWeight: =FontWeight.Semibold
                Height: =25
                Size: =12
                Text: ="Popis"
                Width: =100
                X: =0
                Y: =80
                ZIndex: =3

            DescriptionInput As text:
                BorderColor: =RGBA(206, 212, 218, 1)
                Default: =If(IsBlank(varSelectedItem), "", varSelectedItem.Description)
                Height: =60
                HintText: ="Zadajte popis položky"
                Mode: =TextMode.MultiLine
                Size: =12
                Width: =Parent.Width - 20
                X: =0
                Y: =110
                ZIndex: =4

        StatusSection As groupContainer.verticalAutoLayoutContainer:
            BorderColor: =RGBA(222, 226, 230, 1)
            BorderRadius: =8
            BorderThickness: =1
            Fill: =RGBA(255, 255, 255, 1)
            Height: =120
            LayoutDirection: =LayoutDirection.Vertical
            LayoutGap: =10
            LayoutMode: =LayoutMode.Auto
            PaddingBottom: =16
            PaddingLeft: =16
            PaddingRight: =16
            PaddingTop: =16
            Width: =Parent.Width
            ZIndex: =2

            CategoryRow As groupContainer.horizontalAutoLayoutContainer:
                Height: =35
                LayoutDirection: =LayoutDirection.Horizontal
                LayoutGap: =20
                LayoutMode: =LayoutMode.Auto
                Width: =Parent.Width
                ZIndex: =1

                CategoryLabel As label:
                    Color: =RGBA(33, 37, 41, 1)
                    FontWeight: =FontWeight.Semibold
                    Height: =25
                    Size: =12
                    Text: ="Kategória *"
                    Width: =100
                    X: =0
                    Y: =0
                    ZIndex: =1

                CategoryDropdown As dropdown:
                    DefaultSelectedItems: =If(IsBlank(varSelectedItem), ["Stredná priorita"], [varSelectedItem.Category])
                    Height: =35
                    Items: =["Vysoká priorita", "Stredná priorita", "Nízka priorita"]
                    Size: =12
                    Width: =200
                    X: =0
                    Y: =30
                    ZIndex: =2

                StatusLabel As label:
                    Color: =RGBA(33, 37, 41, 1)
                    FontWeight: =FontWeight.Semibold
                    Height: =25
                    Size: =12
                    Text: ="Stav *"
                    Width: =100
                    X: =220
                    Y: =0
                    ZIndex: =3

                StatusDropdown As dropdown:
                    DefaultSelectedItems: =If(IsBlank(varSelectedItem), ["Nový"], [varSelectedItem.Status])
                    Height: =35
                    Items: =["Nový", "V riešení", "Dokončený", "Zrušený"]
                    Size: =12
                    Width: =200
                    X: =220
                    Y: =30
                    ZIndex: =4

            ProgressRow As groupContainer.horizontalAutoLayoutContainer:
                Height: =25
                LayoutDirection: =LayoutDirection.Horizontal
                LayoutGap: =10
                LayoutMode: =LayoutMode.Auto
                Width: =Parent.Width
                Y: =75
                ZIndex: =2

                ProgressLabel As label:
                    Color: =RGBA(33, 37, 41, 1)
                    FontWeight: =FontWeight.Semibold
                    Height: =25
                    Size: =12
                    Text: ="Pokrok (%)"
                    Width: =100
                    X: =0
                    Y: =75
                    ZIndex: =1

                ProgressSlider As slider:
                    Default: =If(IsBlank(varSelectedItem), 0, varSelectedItem.Progress)
                    HandleFill: =RGBA(0, 120, 212, 1)
                    Height: =25
                    Max: =100
                    Min: =0
                    RailFill: =RGBA(222, 226, 230, 1)
                    Step: =5
                    Width: =200
                    X: =110
                    Y: =75
                    ZIndex: =2

                ProgressValue As label:
                    Align: =Align.Center
                    Color: =RGBA(33, 37, 41, 1)
                    Height: =25
                    Size: =12
                    Text: =ProgressSlider.Value & "%"
                    Width: =50
                    X: =320
                    Y: =75
                    ZIndex: =3

        DetailsSection As groupContainer.verticalAutoLayoutContainer:
            BorderColor: =RGBA(222, 226, 230, 1)
            BorderRadius: =8
            BorderThickness: =1
            Fill: =RGBA(255, 255, 255, 1)
            Height: =120
            LayoutDirection: =LayoutDirection.Vertical
            LayoutGap: =10
            LayoutMode: =LayoutMode.Auto
            PaddingBottom: =16
            PaddingLeft: =16
            PaddingRight: =16
            PaddingTop: =16
            Width: =Parent.Width
            ZIndex: =3

            DateAssignedRow As groupContainer.horizontalAutoLayoutContainer:
                Height: =35
                LayoutDirection: =LayoutDirection.Horizontal
                LayoutGap: =20
                LayoutMode: =LayoutMode.Auto
                Width: =Parent.Width
                ZIndex: =1

                DueDateLabel As label:
                    Color: =RGBA(33, 37, 41, 1)
                    FontWeight: =FontWeight.Semibold
                    Height: =25
                    Size: =12
                    Text: ="Termín dokončenia"
                    Width: =150
                    X: =0
                    Y: =0
                    ZIndex: =1

                DueDatePicker As datePicker:
                    DefaultDate: =If(IsBlank(varSelectedItem) || IsBlank(varSelectedItem.DueDate), Blank(), varSelectedItem.DueDate)
                    Format: =DateTimeFormat.ShortDate
                    Height: =35
                    Size: =12
                    Width: =200
                    X: =0
                    Y: =30
                    ZIndex: =2

                AssignedToLabel As label:
                    Color: =RGBA(33, 37, 41, 1)
                    FontWeight: =FontWeight.Semibold
                    Height: =25
                    Size: =12
                    Text: ="Priradené komu"
                    Width: =150
                    X: =220
                    Y: =0
                    ZIndex: =3

                AssignedToCombo As combobox:
                    DefaultSelectedItems: =If(IsBlank(varSelectedItem) || IsBlank(varSelectedItem.AssignedTo), [], [varSelectedItem.AssignedTo])
                    DisplayFields: =["DisplayName"]
                    Height: =35
                    Items: =Office365Users.SearchUser({searchTerm: ""}).value
                    SearchFields: =["DisplayName", "Mail"]
                    Size: =12
                    Width: =200
                    X: =220
                    Y: =30
                    ZIndex: =4

            TagsRow As groupContainer.horizontalAutoLayoutContainer:
                Height: =35
                LayoutDirection: =LayoutDirection.Horizontal
                LayoutGap: =10
                LayoutMode: =LayoutMode.Auto
                Width: =Parent.Width
                Y: =75
                ZIndex: =2

                TagsLabel As label:
                    Color: =RGBA(33, 37, 41, 1)
                    FontWeight: =FontWeight.Semibold
                    Height: =25
                    Size: =12
                    Text: ="Štítky"
                    Width: =100
                    X: =0
                    Y: =75
                    ZIndex: =1

                TagsInput As text:
                    BorderColor: =RGBA(206, 212, 218, 1)
                    Default: =If(IsBlank(varSelectedItem), "", varSelectedItem.Tags)
                    Height: =35
                    HintText: ="Štítky oddelené čiarkami"
                    Size: =12
                    Width: =310
                    X: =110
                    Y: =75
                    ZIndex: =2
