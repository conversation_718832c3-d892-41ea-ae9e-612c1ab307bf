{"EntityName": "Office365Users", "Type": "Office365UsersEntity", "DisplayName": "Office 365 Users", "LogicalName": "Office 365 Users", "CollectionName": "Office365Users", "EntitySetName": "Office365Users", "IsHidden": false, "IsDisplayNameLocalized": false, "MinVersionToLoad": "1.331", "WadlMetadata": {"BaseUrl": "https://graph.microsoft.com/v1.0", "EntityContainer": {"Name": "Office365UsersContainer", "Extends": "Container"}, "EntitySet": {"Name": "Office365Users", "EntityType": "Office365UsersEntity"}, "EntityType": {"Name": "Office365UsersEntity", "Key": {"PropertyRef": {"Name": "Id"}}, "Properties": [{"Name": "Id", "Type": "Edm.String", "Nullable": false, "ReadOnly": true}, {"Name": "DisplayName", "Type": "Edm.String", "Nullable": true}, {"Name": "Mail", "Type": "Edm.String", "Nullable": true}, {"Name": "UserPrincipalName", "Type": "Edm.String", "Nullable": true}, {"Name": "JobTitle", "Type": "Edm.String", "Nullable": true}, {"Name": "Department", "Type": "Edm.String", "Nullable": true}, {"Name": "OfficeLocation", "Type": "Edm.String", "Nullable": true}, {"Name": "MobilePhone", "Type": "Edm.String", "Nullable": true}]}}, "Schema": {"EntityContainer": "Office365UsersContainer", "EntitySet": "Office365Users", "EntityType": "Office365UsersEntity"}, "Operations": {"SearchUser": true, "GetUserProfile": true, "GetManager": false, "GetDirectReports": false}, "TableDefinition": {"Name": "Office365Users", "DisplayName": "Office 365 Users", "Kind": "Office365Users", "Columns": [{"Name": "Id", "DisplayName": "ID", "Type": "Text", "IsPrimaryKey": true, "IsRequired": false, "IsReadOnly": true}, {"Name": "DisplayName", "DisplayName": "Display Name", "Type": "Text", "IsRequired": false}, {"Name": "Mail", "DisplayName": "Email", "Type": "Text", "IsRequired": false}, {"Name": "UserPrincipalName", "DisplayName": "User Principal Name", "Type": "Text", "IsRequired": false}, {"Name": "JobTitle", "DisplayName": "Job Title", "Type": "Text", "IsRequired": false}, {"Name": "Department", "DisplayName": "Department", "Type": "Text", "IsRequired": false}, {"Name": "OfficeLocation", "DisplayName": "Office Location", "Type": "Text", "IsRequired": false}, {"Name": "MobilePhone", "DisplayName": "Mobile Phone", "Type": "Text", "IsRequired": false}]}}